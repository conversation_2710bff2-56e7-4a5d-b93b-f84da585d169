name: Deploy to Azure Container Apps

on:
  workflow_dispatch:
  push:
    branches: [main]

env:
  AZURE_CONTAINER_REGISTRY: TeyaHealthDev
  CONTAINER_APP_NAME: encounternotes-dev
  RESOURCE_GROUP: teyahealth-rg-dev-eastus-001

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "8.0.x"

      - name: Build
        run: dotnet build Services/EncounterNotesApi/EncounterNotesService.csproj --configuration Release

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to ACR
        uses: docker/login-action@v3
        with:
          registry: ${{ env.AZURE_CONTAINER_REGISTRY }}.azurecr.io
          username: TeyaHealthDev
          password: ZUBdj9I6ptHQ/NbSE5+S/H4F+nURzPN5Xi+qxokqwC+ACRCOu012

      - name: Build and push container image to ACR
        uses: docker/build-push-action@v6
        with:
          push: true
          tags: ${{ env.AZURE_CONTAINER_REGISTRY }}.azurecr.io/${{ env.CONTAINER_APP_NAME }}:${{ github.sha }}
          file: Services/EncounterNotesApi/Dockerfile

      - name: Azure Login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.DEVAZURECREDSECRET }}

      - name: Deploy to Azure Container Apps
        uses: azure/container-apps-deploy-action@v1
        with:
          imageToDeploy: ${{ env.AZURE_CONTAINER_REGISTRY }}.azurecr.io/${{ env.CONTAINER_APP_NAME }}:${{ github.sha }}
          resourceGroup: ${{ env.RESOURCE_GROUP }}
          containerAppName: ${{ env.CONTAINER_APP_NAME }}
          environmentVariables: |
            ASPNETCORE_ENVIRONMENT=Development

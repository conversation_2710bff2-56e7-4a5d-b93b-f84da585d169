@echo off
REM Teya Health Build Script with Automatic Versioning (Batch wrapper)
REM This batch file calls the PowerShell script with the same arguments

setlocal enabledelayedexpansion

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo Error: PowerShell is not available
    echo Please install PowerShell or use the PowerShell script directly
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Build the PowerShell command with all arguments
set "PS_ARGS="
:argloop
if "%~1"=="" goto :endargs
set "PS_ARGS=!PS_ARGS! '%~1'"
shift
goto :argloop
:endargs

REM Execute the PowerShell script
powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%BuildWithVersion.ps1" %PS_ARGS%

REM Exit with the same code as PowerShell
exit /b %errorlevel%

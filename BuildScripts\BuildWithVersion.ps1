# Teya Health Build Script with Automatic Versioning
# This script builds the entire solution with automatic date-based versioning

param(
    [string]$Configuration = "Release",
    [string]$Project = "",
    [switch]$Clean = $false,
    [switch]$Restore = $true,
    [switch]$ShowVersion = $false,
    [switch]$Help = $false
)

function Show-Help {
    Write-Host "Teya Health Build Script with Automatic Versioning" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\BuildWithVersion.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Configuration <config>  Build configuration (Debug/Release). Default: Release"
    Write-Host "  -Project <path>          Specific project to build. Default: entire solution"
    Write-Host "  -Clean                   Clean before building"
    Write-Host "  -Restore                 Restore packages before building. Default: true"
    Write-Host "  -ShowVersion             Show version information only"
    Write-Host "  -Help                    Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\BuildWithVersion.ps1                                    # Build entire solution"
    Write-Host "  .\BuildWithVersion.ps1 -Configuration Debug              # Build in Debug mode"
    Write-Host "  .\BuildWithVersion.ps1 -Project Services/AppointmentsApi # Build specific project"
    Write-Host "  .\BuildWithVersion.ps1 -Clean                            # Clean and build"
    Write-Host "  .\BuildWithVersion.ps1 -ShowVersion                      # Show version only"
}

function Get-BuildVersion {
    $buildNumberFile = "build-number.txt"
    $majorVersion = 1
    $minorVersion = 0
    $patchVersion = 0

    # Read current build number
    $buildNumber = 1
    if (Test-Path $buildNumberFile) {
        try {
            $buildNumber = [int](Get-Content $buildNumberFile -Raw).Trim()
        }
        catch {
            $buildNumber = 1
        }
    }

    return "$majorVersion.$minorVersion.$patchVersion.$buildNumber"
}

function Show-VersionInfo {
    $version = Get-BuildVersion
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    Write-Host "=== Teya Health Build Version Information ===" -ForegroundColor Cyan
    Write-Host "Version: $version" -ForegroundColor Yellow
    Write-Host "Build Date: $timestamp" -ForegroundColor Yellow
    Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
}

function Test-DotNetInstalled {
    try {
        $dotnetVersion = dotnet --version
        Write-Host "Using .NET SDK version: $dotnetVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error: .NET SDK is not installed or not in PATH" -ForegroundColor Red
        return $false
    }
}

function Build-Solution {
    param(
        [string]$Target,
        [string]$Config
    )
    
    $buildTarget = if ($Target) { $Target } else { "TeyaSource.sln" }
    
    Write-Host "Building: $buildTarget" -ForegroundColor Green
    Write-Host "Configuration: $Config" -ForegroundColor Green
    Write-Host ""
    
    if ($Clean) {
        Write-Host "Cleaning..." -ForegroundColor Yellow
        dotnet clean $buildTarget --configuration $Config
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Clean failed!" -ForegroundColor Red
            return $false
        }
    }
    
    if ($Restore) {
        Write-Host "Restoring packages..." -ForegroundColor Yellow
        dotnet restore $buildTarget
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Package restore failed!" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "Building with automatic versioning..." -ForegroundColor Yellow
    dotnet build $buildTarget --configuration $Config --no-restore:$(!$Restore)
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        return $false
    }
}

# Main script execution
if ($Help) {
    Show-Help
    exit 0
}

if ($ShowVersion) {
    Show-VersionInfo
    exit 0
}

# Check if .NET is installed
if (-not (Test-DotNetInstalled)) {
    exit 1
}

# Show version information
Show-VersionInfo

# Build the solution or specific project
$success = Build-Solution -Target $Project -Config $Configuration

if ($success) {
    Write-Host ""
    Write-Host "=== Build Summary ===" -ForegroundColor Cyan
    Write-Host "Status: SUCCESS" -ForegroundColor Green
    Write-Host "Version: $(Get-BuildVersion)" -ForegroundColor Yellow
    Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
    Write-Host "===================" -ForegroundColor Cyan
    exit 0
} else {
    Write-Host ""
    Write-Host "=== Build Summary ===" -ForegroundColor Cyan
    Write-Host "Status: FAILED" -ForegroundColor Red
    Write-Host "===================" -ForegroundColor Cyan
    exit 1
}

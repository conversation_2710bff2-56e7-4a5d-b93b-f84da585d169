#!/bin/bash

# Teya Health Build Script with Automatic Versioning (Linux/CI)
# This script builds the entire solution with automatic date-based versioning

set -e  # Exit on any error

# Default values
CONFIGURATION="Release"
PROJECT=""
CLEAN=false
RESTORE=true
SHOW_VERSION=false
HELP=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to show help
show_help() {
    echo -e "${GREEN}Teya Health Build Script with Automatic Versioning${NC}"
    echo ""
    echo "Usage: ./BuildWithVersion.sh [options]"
    echo ""
    echo "Options:"
    echo "  -c, --configuration <config>  Build configuration (Debug/Release). Default: Release"
    echo "  -p, --project <path>          Specific project to build. Default: entire solution"
    echo "  -l, --clean                   Clean before building"
    echo "  -n, --no-restore              Skip package restore"
    echo "  -v, --show-version            Show version information only"
    echo "  -h, --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./BuildWithVersion.sh                                    # Build entire solution"
    echo "  ./BuildWithVersion.sh -c Debug                          # Build in Debug mode"
    echo "  ./BuildWithVersion.sh -p Services/AppointmentsApi       # Build specific project"
    echo "  ./BuildWithVersion.sh -l                                # Clean and build"
    echo "  ./BuildWithVersion.sh -v                                # Show version only"
}

# Function to get build version
get_build_version() {
    local build_number_file="build-number.txt"
    local major_version=1
    local minor_version=0
    local patch_version=0

    # Read current build number
    local build_number=1
    if [ -f "$build_number_file" ]; then
        build_number=$(cat "$build_number_file" | tr -d '[:space:]')
        if ! [[ "$build_number" =~ ^[0-9]+$ ]]; then
            build_number=1
        fi
    fi

    echo "$major_version.$minor_version.$patch_version.$build_number"
}

# Function to show version information
show_version_info() {
    local version=$(get_build_version)
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    
    echo -e "${CYAN}=== Teya Health Build Version Information ===${NC}"
    echo -e "${YELLOW}Version: $version${NC}"
    echo -e "${YELLOW}Build Date: $timestamp${NC}"
    echo -e "${YELLOW}Configuration: $CONFIGURATION${NC}"
    echo -e "${CYAN}=============================================${NC}"
    echo ""
}

# Function to test if .NET is installed
test_dotnet_installed() {
    if command -v dotnet &> /dev/null; then
        local dotnet_version=$(dotnet --version)
        echo -e "${GREEN}Using .NET SDK version: $dotnet_version${NC}"
        return 0
    else
        echo -e "${RED}Error: .NET SDK is not installed or not in PATH${NC}"
        return 1
    fi
}

# Function to build solution
build_solution() {
    local target="${1:-TeyaSource.sln}"
    local config="$2"
    
    echo -e "${GREEN}Building: $target${NC}"
    echo -e "${GREEN}Configuration: $config${NC}"
    echo ""
    
    if [ "$CLEAN" = true ]; then
        echo -e "${YELLOW}Cleaning...${NC}"
        dotnet clean "$target" --configuration "$config"
    fi
    
    if [ "$RESTORE" = true ]; then
        echo -e "${YELLOW}Restoring packages...${NC}"
        dotnet restore "$target"
    fi
    
    echo -e "${YELLOW}Building with automatic versioning...${NC}"
    local restore_flag=""
    if [ "$RESTORE" = false ]; then
        restore_flag="--no-restore"
    fi
    
    if dotnet build "$target" --configuration "$config" $restore_flag; then
        echo -e "${GREEN}Build completed successfully!${NC}"
        return 0
    else
        echo -e "${RED}Build failed!${NC}"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--configuration)
            CONFIGURATION="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT="$2"
            shift 2
            ;;
        -l|--clean)
            CLEAN=true
            shift
            ;;
        -n|--no-restore)
            RESTORE=false
            shift
            ;;
        -v|--show-version)
            SHOW_VERSION=true
            shift
            ;;
        -h|--help)
            HELP=true
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Main script execution
if [ "$HELP" = true ]; then
    show_help
    exit 0
fi

if [ "$SHOW_VERSION" = true ]; then
    show_version_info
    exit 0
fi

# Check if .NET is installed
if ! test_dotnet_installed; then
    exit 1
fi

# Show version information
show_version_info

# Build the solution or specific project
if build_solution "$PROJECT" "$CONFIGURATION"; then
    echo ""
    echo -e "${CYAN}=== Build Summary ===${NC}"
    echo -e "${GREEN}Status: SUCCESS${NC}"
    echo -e "${YELLOW}Version: $(get_build_version)${NC}"
    echo -e "${YELLOW}Configuration: $CONFIGURATION${NC}"
    echo -e "${CYAN}===================${NC}"
    exit 0
else
    echo ""
    echo -e "${CYAN}=== Build Summary ===${NC}"
    echo -e "${RED}Status: FAILED${NC}"
    echo -e "${CYAN}===================${NC}"
    exit 1
fi

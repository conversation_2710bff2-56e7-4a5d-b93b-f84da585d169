# Teya Health Enterprise Build System
# Following Microsoft/Google/Apple enterprise build patterns

param(
    [string]$Configuration = "Release",
    [string]$Project = "",
    [int]$ProductMajor = 1,
    [int]$ProductMinor = 0,
    [switch]$Clean = $false,
    [switch]$Restore = $true,
    [switch]$ShowVersion = $false,
    [switch]$CreateRelease = $false,
    [switch]$Help = $false
)

function Show-Help {
    Write-Host "Teya Health Enterprise Build System" -ForegroundColor Green
    Write-Host "Following Microsoft/Google/Apple enterprise patterns" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\EnterpriseBuild.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Configuration <config>    Build configuration (Debug/Release). Default: Release"
    Write-Host "  -Project <path>           Specific project to build. Default: entire solution"
    Write-Host "  -ProductMajor <number>    Product major version. Default: 1"
    Write-Host "  -ProductMinor <number>    Product minor version. Default: 0"
    Write-Host "  -Clean                    Clean before building"
    Write-Host "  -Restore                  Restore packages before building. Default: true"
    Write-Host "  -ShowVersion              Show version information only"
    Write-Host "  -CreateRelease            Create release package"
    Write-Host "  -Help                     Show this help message"
    Write-Host ""
    Write-Host "Version Format: Major.Minor.Build.Revision"
    Write-Host "  Major.Minor: Product version (manually managed)"
    Write-Host "  Build: Auto-generated (YearDay format like Microsoft)"
    Write-Host "  Revision: Daily build counter (resets each day)"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\EnterpriseBuild.ps1                                    # Build entire solution"
    Write-Host "  .\EnterpriseBuild.ps1 -ProductMajor 2 -ProductMinor 1   # Build v2.1.x.x"
    Write-Host "  .\EnterpriseBuild.ps1 -CreateRelease                    # Create release build"
    Write-Host "  .\EnterpriseBuild.ps1 -ShowVersion                      # Show version only"
}

function Get-EnterpriseVersion {
    param(
        [int]$Major = 1,
        [int]$Minor = 0
    )
    
    $now = Get-Date
    $year = $now.ToString("yy")
    $dayOfYear = $now.DayOfYear.ToString("000")
    $buildNumber = "$year$dayOfYear"
    
    # Read daily build counter
    $buildDir = ".build"
    $counterFile = "$buildDir\build-counter.txt"
    $todayFile = "$buildDir\today.txt"
    
    if (-not (Test-Path $buildDir)) {
        New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
    }
    
    $today = $buildNumber
    $lastBuildDay = ""
    $revision = 1
    
    if (Test-Path $todayFile) {
        $lastBuildDay = (Get-Content $todayFile -Raw).Trim()
    }
    
    if ((Test-Path $counterFile) -and ($today -eq $lastBuildDay)) {
        try {
            $revision = [int](Get-Content $counterFile -Raw).Trim()
        }
        catch {
            $revision = 1
        }
    }
    
    return @{
        ProductMajor = $Major
        ProductMinor = $Minor
        BuildNumber = $buildNumber
        Revision = $revision
        Version = "$Major.$Minor.$buildNumber.$revision"
        Year = "20$year"
        DayOfYear = [int]$dayOfYear
        BuildDate = $now.ToString("yyyy-MM-dd HH:mm:ss")
    }
}

function Show-EnterpriseVersionInfo {
    param($VersionInfo, $Config)
    
    Write-Host ""
    Write-Host "==================== TEYA HEALTH BUILD ====================" -ForegroundColor Cyan
    Write-Host "Product Version: $($VersionInfo.ProductMajor).$($VersionInfo.ProductMinor)" -ForegroundColor Yellow
    Write-Host "Full Version: $($VersionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Build Number: $($VersionInfo.BuildNumber) (Year: $($VersionInfo.Year), Day: $($VersionInfo.DayOfYear))" -ForegroundColor White
    Write-Host "Revision: $($VersionInfo.Revision) (Daily build #$($VersionInfo.Revision))" -ForegroundColor White
    Write-Host "Configuration: $Config" -ForegroundColor White
    Write-Host "Build Time: $($VersionInfo.BuildDate)" -ForegroundColor White
    Write-Host "=========================================================" -ForegroundColor Cyan
    Write-Host ""
}

function Test-DotNetInstalled {
    try {
        $dotnetVersion = dotnet --version
        Write-Host "Using .NET SDK version: $dotnetVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error: .NET SDK is not installed or not in PATH" -ForegroundColor Red
        return $false
    }
}

function Build-Enterprise {
    param(
        [string]$Target,
        [string]$Config,
        [int]$Major,
        [int]$Minor
    )
    
    $buildTarget = if ($Target) { $Target } else { "TeyaSource.sln" }
    
    Write-Host "Building: $buildTarget" -ForegroundColor Green
    Write-Host "Configuration: $Config" -ForegroundColor Green
    Write-Host ""
    
    if ($Clean) {
        Write-Host "Cleaning..." -ForegroundColor Yellow
        dotnet clean $buildTarget --configuration $Config
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Clean failed!" -ForegroundColor Red
            return $false
        }
    }
    
    if ($Restore) {
        Write-Host "Restoring packages..." -ForegroundColor Yellow
        dotnet restore $buildTarget
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Package restore failed!" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "Building with enterprise versioning..." -ForegroundColor Yellow
    $msbuildArgs = @(
        $buildTarget
        "--configuration", $Config
        "-p:ProductMajor=$Major"
        "-p:ProductMinor=$Minor"
    )
    
    if (-not $Restore) {
        $msbuildArgs += "--no-restore"
    }
    
    dotnet build @msbuildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        return $false
    }
}

function Create-ReleasePackage {
    param($VersionInfo)
    
    Write-Host "Creating release package..." -ForegroundColor Yellow
    
    $releaseDir = "releases\v$($VersionInfo.Version)"
    if (-not (Test-Path $releaseDir)) {
        New-Item -ItemType Directory -Path $releaseDir -Force | Out-Null
    }
    
    # Create release notes
    $releaseNotes = @"
Teya Health Platform v$($VersionInfo.Version)
Built on $($VersionInfo.BuildDate)
Configuration: $Configuration

Build Information:
- Product Version: $($VersionInfo.ProductMajor).$($VersionInfo.ProductMinor)
- Build Number: $($VersionInfo.BuildNumber)
- Revision: $($VersionInfo.Revision)
- Year: $($VersionInfo.Year)
- Day of Year: $($VersionInfo.DayOfYear)

This is an enterprise build following Microsoft/Google/Apple patterns.
"@
    
    $releaseNotes | Out-File "$releaseDir\RELEASE-NOTES.txt" -Encoding UTF8
    
    Write-Host "Release package created in: $releaseDir" -ForegroundColor Green
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

# Check if .NET is installed
if (-not (Test-DotNetInstalled)) {
    exit 1
}

# Get enterprise version information
$versionInfo = Get-EnterpriseVersion -Major $ProductMajor -Minor $ProductMinor

if ($ShowVersion) {
    Show-EnterpriseVersionInfo -VersionInfo $versionInfo -Config $Configuration
    exit 0
}

# Show version information
Show-EnterpriseVersionInfo -VersionInfo $versionInfo -Config $Configuration

# Build the solution or specific project
$success = Build-Enterprise -Target $Project -Config $Configuration -Major $ProductMajor -Minor $ProductMinor

if ($success) {
    # Get updated version info after build
    $finalVersionInfo = Get-EnterpriseVersion -Major $ProductMajor -Minor $ProductMinor
    
    if ($CreateRelease) {
        Create-ReleasePackage -VersionInfo $finalVersionInfo
    }
    
    Write-Host ""
    Write-Host "==================== BUILD SUMMARY ====================" -ForegroundColor Cyan
    Write-Host "Status: SUCCESS" -ForegroundColor Green
    Write-Host "Version: $($finalVersionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
    Write-Host "=======================================================" -ForegroundColor Cyan
    exit 0
} else {
    Write-Host ""
    Write-Host "==================== BUILD SUMMARY ====================" -ForegroundColor Cyan
    Write-Host "Status: FAILED" -ForegroundColor Red
    Write-Host "=======================================================" -ForegroundColor Cyan
    exit 1
}

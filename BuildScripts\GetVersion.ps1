# Get Version Information Script
# This script returns the current build version that would be used

param(
    [switch]$Json = $false,
    [switch]$Short = $false,
    [switch]$Help = $false
)

function Show-Help {
    Write-Host "Get Version Information Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\GetVersion.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Json     Output version information in JSON format"
    Write-Host "  -Short    Output only the version number"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\GetVersion.ps1           # Show detailed version info"
    Write-Host "  .\GetVersion.ps1 -Short    # Show only version number"
    Write-Host "  .\GetVersion.ps1 -Json     # Output as JSON"
}

function Get-BuildVersion {
    $buildNumberFile = "build-number.txt"
    $majorVersion = 1
    $minorVersion = 0
    $patchVersion = 0

    # Read current build number
    $buildNumber = 1
    if (Test-Path $buildNumberFile) {
        try {
            $buildNumber = [int](Get-Content $buildNumberFile -Raw).Trim()
        }
        catch {
            $buildNumber = 1
        }
    }

    $now = Get-Date

    return @{
        Version = "$majorVersion.$minorVersion.$patchVersion.$buildNumber"
        MajorVersion = $majorVersion
        MinorVersion = $minorVersion
        PatchVersion = $patchVersion
        BuildNumber = $buildNumber
        BuildDate = $now.ToString("yyyy-MM-dd HH:mm:ss")
        Timestamp = $now
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

$versionInfo = Get-BuildVersion

if ($Short) {
    Write-Output $versionInfo.Version
} elseif ($Json) {
    $jsonOutput = @{
        version = $versionInfo.Version
        buildNumber = $versionInfo.BuildNumber
        majorVersion = $versionInfo.MajorVersion
        minorVersion = $versionInfo.MinorVersion
        patchVersion = $versionInfo.PatchVersion
        buildDate = $versionInfo.BuildDate
    } | ConvertTo-Json -Depth 2

    Write-Output $jsonOutput
} else {
    Write-Host "Teya Health Build Version Information" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    Write-Host "Version: $($versionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Build Number: $($versionInfo.BuildNumber)" -ForegroundColor Yellow
    Write-Host "Build Date: $($versionInfo.BuildDate)" -ForegroundColor Yellow
    Write-Host "Major: $($versionInfo.MajorVersion)" -ForegroundColor White
    Write-Host "Minor: $($versionInfo.MinorVersion)" -ForegroundColor White
    Write-Host "Patch: $($versionInfo.PatchVersion)" -ForegroundColor White
    Write-Host "====================================" -ForegroundColor Cyan
}

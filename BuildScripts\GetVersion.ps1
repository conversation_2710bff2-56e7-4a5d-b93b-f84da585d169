# Get Version Information Script
# This script returns the current build version that would be used

param(
    [switch]$Json = $false,
    [switch]$Short = $false,
    [switch]$Help = $false
)

function Show-Help {
    Write-Host "Get Version Information Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\GetVersion.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Json     Output version information in JSON format"
    Write-Host "  -Short    Output only the version number"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\GetVersion.ps1           # Show detailed version info"
    Write-Host "  .\GetVersion.ps1 -Short    # Show only version number"
    Write-Host "  .\GetVersion.ps1 -Json     # Output as JSON"
}

function Get-BuildVersion {
    $now = Get-Date
    $year = $now.ToString("yyyy")
    $month = $now.ToString("MM")
    $day = $now.ToString("dd")
    $time = $now.ToString("HHmm")
    
    return @{
        Version = "$year.$month.$day.$time"
        Year = $year
        Month = $month
        Day = $day
        Time = $time
        BuildDate = $now.ToString("yyyy-MM-dd HH:mm:ss")
        Timestamp = $now
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

$versionInfo = Get-BuildVersion

if ($Short) {
    Write-Output $versionInfo.Version
} elseif ($Json) {
    $jsonOutput = @{
        version = $versionInfo.Version
        buildDate = $versionInfo.BuildDate
        buildYear = $versionInfo.Year
        buildMonth = $versionInfo.Month
        buildDay = $versionInfo.Day
        buildTime = $versionInfo.Time
    } | ConvertTo-Json -Depth 2
    
    Write-Output $jsonOutput
} else {
    Write-Host "Teya Health Build Version Information" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    Write-Host "Version: $($versionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Build Date: $($versionInfo.BuildDate)" -ForegroundColor Yellow
    Write-Host "Year: $($versionInfo.Year)" -ForegroundColor White
    Write-Host "Month: $($versionInfo.Month)" -ForegroundColor White
    Write-Host "Day: $($versionInfo.Day)" -ForegroundColor White
    Write-Host "Time: $($versionInfo.Time)" -ForegroundColor White
    Write-Host "====================================" -ForegroundColor Cyan
}

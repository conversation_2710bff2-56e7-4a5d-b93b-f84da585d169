# Teya Health Enterprise Build System
# Professional versioning following industry standards

param(
    [string]$Configuration = "Release",
    [string]$Project = "",
    [int]$MajorVersion = 1,
    [int]$MinorVersion = 0,
    [switch]$Clean = $false,
    [switch]$Restore = $true,
    [switch]$ShowVersion = $false,
    [switch]$Help = $false
)

function Show-Help {
    Write-Host "Teya Health Enterprise Build System" -ForegroundColor Green
    Write-Host "Professional versioning following industry standards" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\TeyaBuild.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Configuration <config>    Build configuration (Debug/Release). Default: Release"
    Write-Host "  -Project <path>           Specific project to build. Default: entire solution"
    Write-Host "  -MajorVersion <number>    Major version. Default: 1"
    Write-Host "  -MinorVersion <number>    Minor version. Default: 0"
    Write-Host "  -Clean                    Clean before building"
    Write-Host "  -Restore                  Restore packages before building. Default: true"
    Write-Host "  -ShowVersion              Show version information only"
    Write-Host "  -Help                     Show this help message"
    Write-Host ""
    Write-Host "TeyaHealth Version Format: Major.Minor.Build.Revision"
    Write-Host "  Major.Minor: Product version"
    Write-Host "  Build: YMMDD format (Year + Month + Day)"
    Write-Host "  Revision: Daily build counter"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  Industry Standard: 1.0.50605.4"
    Write-Host "  Enterprise Format: Major.Minor.Build.Revision"
    Write-Host ""
    Write-Host "Usage Examples:"
    Write-Host "  .\TeyaBuild.ps1                                    # Build v1.0.x.x"
    Write-Host "  .\TeyaBuild.ps1 -MajorVersion 2 -MinorVersion 1   # Build v2.1.x.x"
    Write-Host "  .\TeyaBuild.ps1 -ShowVersion                      # Show version only"
}

function Get-TeyaVersion {
    param(
        [int]$Major = 1,
        [int]$Minor = 0
    )
    
    $now = Get-Date
    
    # TeyaHealth Build Number Pattern: YMMDD
    # Y = Last digit of year (5 for 2025)
    # MM = Month (01-12)
    # DD = Day (01-31)
    $year = $now.Year.ToString().Substring(3, 1)  # Last digit of year (5 for 2025)
    $month = $now.ToString("MM")                   # Month with leading zero
    $day = $now.ToString("dd")                     # Day with leading zero
    $buildNumber = "$year$month$day"
    
    # Read daily build counter
    $buildDir = ".build"
    $counterFile = "$buildDir\build-counter.txt"
    $todayFile = "$buildDir\today.txt"
    
    if (-not (Test-Path $buildDir)) {
        New-Item -ItemType Directory -Path $buildDir -Force | Out-Null
    }
    
    $today = $buildNumber
    $lastBuildDay = ""
    $revision = 1
    
    if (Test-Path $todayFile) {
        $lastBuildDay = (Get-Content $todayFile -Raw).Trim()
    }
    
    if ((Test-Path $counterFile) -and ($today -eq $lastBuildDay)) {
        try {
            $revision = [int](Get-Content $counterFile -Raw).Trim()
        }
        catch {
            $revision = 1
        }
    }
    
    return @{
        MajorVersion = $Major
        MinorVersion = $Minor
        BuildNumber = $buildNumber
        Revision = $revision
        Version = "$Major.$Minor.$buildNumber.$revision"
        Year = $now.Year.ToString()
        Month = $month
        Day = $day
        BuildDate = $now.ToString("yyyy-MM-dd HH:mm:ss")
    }
}

function Show-TeyaVersionInfo {
    param($VersionInfo, $Config)
    
    Write-Host ""
    Write-Host "==================== TEYA HEALTH BUILD ====================" -ForegroundColor Cyan
    Write-Host "Version: $($VersionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Product Version: $($VersionInfo.MajorVersion).$($VersionInfo.MinorVersion)" -ForegroundColor White
    Write-Host "Build Number: $($VersionInfo.BuildNumber) ($($VersionInfo.Year)/$($VersionInfo.Month)/$($VersionInfo.Day))" -ForegroundColor White
    Write-Host "Revision: $($VersionInfo.Revision) (Build #$($VersionInfo.Revision) today)" -ForegroundColor White
    Write-Host "Configuration: $Config" -ForegroundColor White
    Write-Host "Build Time: $($VersionInfo.BuildDate)" -ForegroundColor White
    Write-Host ""
    Write-Host "Enterprise Standard Format: Major.Minor.Build.Revision" -ForegroundColor Gray
    Write-Host "=============================================================" -ForegroundColor Cyan
    Write-Host ""
}

function Test-DotNetInstalled {
    try {
        $dotnetVersion = dotnet --version
        Write-Host "Using .NET SDK version: $dotnetVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error: .NET SDK is not installed or not in PATH" -ForegroundColor Red
        return $false
    }
}

function Build-TeyaProject {
    param(
        [string]$Target,
        [string]$Config,
        [int]$Major,
        [int]$Minor
    )
    
    $buildTarget = if ($Target) { $Target } else { "TeyaSource.sln" }
    
    Write-Host "Building: $buildTarget" -ForegroundColor Green
    Write-Host "Configuration: $Config" -ForegroundColor Green
    Write-Host ""
    
    if ($Clean) {
        Write-Host "Cleaning..." -ForegroundColor Yellow
        dotnet clean $buildTarget --configuration $Config
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Clean failed!" -ForegroundColor Red
            return $false
        }
    }
    
    if ($Restore) {
        Write-Host "Restoring packages..." -ForegroundColor Yellow
        dotnet restore $buildTarget
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Package restore failed!" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "Building with TeyaHealth enterprise versioning..." -ForegroundColor Yellow
    $msbuildArgs = @(
        $buildTarget
        "--configuration", $Config
        "-p:MajorVersion=$Major"
        "-p:MinorVersion=$Minor"
    )
    
    if (-not $Restore) {
        $msbuildArgs += "--no-restore"
    }
    
    dotnet build @msbuildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        return $false
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

# Check if .NET is installed
if (-not (Test-DotNetInstalled)) {
    exit 1
}

# Get TeyaHealth version information
$versionInfo = Get-TeyaVersion -Major $MajorVersion -Minor $MinorVersion

if ($ShowVersion) {
    Show-TeyaVersionInfo -VersionInfo $versionInfo -Config $Configuration
    exit 0
}

# Show version information
Show-TeyaVersionInfo -VersionInfo $versionInfo -Config $Configuration

# Build the solution or specific project
$success = Build-TeyaProject -Target $Project -Config $Configuration -Major $MajorVersion -Minor $MinorVersion

if ($success) {
    # Get updated version info after build
    $finalVersionInfo = Get-TeyaVersion -Major $MajorVersion -Minor $MinorVersion
    
    Write-Host ""
    Write-Host "==================== BUILD SUMMARY ====================" -ForegroundColor Cyan
    Write-Host "Status: SUCCESS" -ForegroundColor Green
    Write-Host "TeyaHealth Version: $($finalVersionInfo.Version)" -ForegroundColor Yellow
    Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
    Write-Host "=======================================================" -ForegroundColor Cyan
    exit 0
} else {
    Write-Host ""
    Write-Host "==================== BUILD SUMMARY ====================" -ForegroundColor Cyan
    Write-Host "Status: FAILED" -ForegroundColor Red
    Write-Host "=======================================================" -ForegroundColor Cyan
    exit 1
}

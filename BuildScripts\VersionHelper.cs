using System;
using System.IO;
using System.Reflection;
using System.Text.Json;

namespace TeyaHealth.Common
{
    /// <summary>
    /// Helper class to access build version information at runtime
    /// </summary>
    public static class VersionHelper
    {
        /// <summary>
        /// Version information model
        /// </summary>
        public class VersionInfo
        {
            public string Version { get; set; } = string.Empty;
            public string BuildDate { get; set; } = string.Empty;
            public string BuildYear { get; set; } = string.Empty;
            public string BuildMonth { get; set; } = string.Empty;
            public string BuildDay { get; set; } = string.Empty;
            public string BuildTime { get; set; } = string.Empty;
        }

        /// <summary>
        /// Gets version information from assembly attributes
        /// </summary>
        /// <returns>Version information from assembly</returns>
        public static string GetAssemblyVersion()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion;
            return version ?? assembly.GetName().Version?.ToString() ?? "Unknown";
        }

        /// <summary>
        /// Gets version information from the version.json file
        /// </summary>
        /// <returns>Version information from file, or null if file not found</returns>
        public static VersionInfo? GetVersionFromFile()
        {
            try
            {
                var versionFilePath = Path.Combine(AppContext.BaseDirectory, "version.json");
                if (!File.Exists(versionFilePath))
                {
                    return null;
                }

                var jsonContent = File.ReadAllText(versionFilePath);
                return JsonSerializer.Deserialize<VersionInfo>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the current version, preferring file version over assembly version
        /// </summary>
        /// <returns>Current version string</returns>
        public static string GetCurrentVersion()
        {
            var fileVersion = GetVersionFromFile();
            if (fileVersion != null && !string.IsNullOrEmpty(fileVersion.Version))
            {
                return fileVersion.Version;
            }

            return GetAssemblyVersion();
        }

        /// <summary>
        /// Gets detailed version information
        /// </summary>
        /// <returns>Detailed version information</returns>
        public static VersionInfo GetDetailedVersionInfo()
        {
            var fileVersion = GetVersionFromFile();
            if (fileVersion != null)
            {
                return fileVersion;
            }

            // Fallback to assembly version
            var assemblyVersion = GetAssemblyVersion();
            return new VersionInfo
            {
                Version = assemblyVersion,
                BuildDate = "Unknown",
                BuildYear = "Unknown",
                BuildMonth = "Unknown",
                BuildDay = "Unknown",
                BuildTime = "Unknown"
            };
        }

        /// <summary>
        /// Formats version information for display
        /// </summary>
        /// <returns>Formatted version string</returns>
        public static string GetFormattedVersionInfo()
        {
            var versionInfo = GetDetailedVersionInfo();
            return $"Version: {versionInfo.Version} (Built: {versionInfo.BuildDate})";
        }
    }
}

// Example usage in a web application (Program.cs or Startup.cs):
/*
// Add version information to application
app.MapGet("/version", () => 
{
    var versionInfo = VersionHelper.GetDetailedVersionInfo();
    return Results.Ok(versionInfo);
});

// Or in a controller:
[ApiController]
[Route("api/[controller]")]
public class VersionController : ControllerBase
{
    [HttpGet]
    public IActionResult GetVersion()
    {
        var versionInfo = VersionHelper.GetDetailedVersionInfo();
        return Ok(versionInfo);
    }
}

// Or in a console application:
Console.WriteLine($"Application {VersionHelper.GetFormattedVersionInfo()}");
*/

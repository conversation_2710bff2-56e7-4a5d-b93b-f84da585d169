﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:TeyaAiScribeMobile"
             xmlns:converters="clr-namespace:TeyaAiScribeMobile.Converters"
             x:Class="TeyaAiScribeMobile.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <Style TargetType="Button" x:Key="rounded-button" />
            <Style TargetType="Frame" x:Key="frame-shadow" />

            <!-- Converters -->
            <converters:RecordingStateToColorConverter x:Key="RecordingStateToColorConverter" />
            <converters:RecordingStateToButtonEnabledConverter x:Key="RecordingStateToButtonEnabledConverter" />
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>

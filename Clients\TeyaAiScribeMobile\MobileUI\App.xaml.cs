﻿using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
namespace TeyaAiScribeMobile
{
    public partial class App : Application
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly NavigationManager _navigationManager;
        private readonly ILogger<App>? _logger;

        public App(IServiceProvider serviceProvider, NavigationManager navigationManager)
        {
            _serviceProvider = serviceProvider;
            _navigationManager = navigationManager;
            MainPage = new AppShell();//when sign in page implemented, add signIn page here.
            _logger = MainPage?.Handler?.MauiContext?.Services.GetService<ILogger<App>>();
            InitializeComponent();
        }

        //protected override Window CreateWindow(IActivationState activationState)
        //{
        //    //var navigationPage = new NavigationPage(new SignIn(_navigationManager));
        //    var navigationPage = new NavigationPage(new MainPage(_navigationManager));
        //    return new Window(navigationPage);  // add when SignIn page is ready for both iOS and Android
        //}

        protected override Window CreateWindow(IActivationState? activationState)
        {
            var window = base.CreateWindow(activationState);

            window.Created += (s, e) =>
            {
                _logger?.LogInformation("Window created");
            };

            window.Activated += (s, e) =>
            {
                _logger?.LogInformation("Window activated");
            };

            window.Deactivated += (s, e) =>
            {
                _logger?.LogInformation("Window deactivated");
            };

            window.Stopped += (s, e) =>
            {
                _logger?.LogInformation("Window stopped");
            };

            window.Resumed += (s, e) =>
            {
                _logger?.LogInformation("Window resumed");
            };

            window.Destroying += (s, e) =>
            {
                _logger?.LogInformation("Window destroying");
            };

            return window;
        }

        public static Page GetRootPage()
        {
            if (Current?.Windows?.Count > 0 && Current.Windows[0].Page is NavigationPage navigationPage)
            {
                return navigationPage.RootPage;
            }
            return null;
        }
    }
}
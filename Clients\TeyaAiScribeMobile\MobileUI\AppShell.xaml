<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="TeyaAiScribeMobile.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:TeyaAiScribeMobile"
    xmlns:strings="clr-namespace:TeyaAiScribeMobile.TeyaAiScribeMobileResource"
    Shell.FlyoutItemIsVisible="True"
    Shell.FlyoutBehavior="Flyout"
    FlyoutBackgroundColor="LightCyan"
    FlyoutWidth="250"
    Title="TeyaAiScribeMobile">

    <Shell.FlyoutHeader>
        <Label Text="Welcome to Teya AI Scribe"
               FontSize="18"
               HorizontalTextAlignment="Center"
               Padding="10" />
    </Shell.FlyoutHeader>

    <FlyoutItem Title="Home" Route="MainPage">
        <ShellContent ContentTemplate="{DataTemplate local:MainPage}" />
    </FlyoutItem>
    
    <ShellContent
        Title="Sign In"
        ContentTemplate="{DataTemplate local:SignIn}"
        Route="SignIn" />

    <ShellContent
         Title="UserSettings"
         ContentTemplate="{DataTemplate local:UserSettings}"
         Route="UserSettings" />
    
    <ShellContent
         Title="Message"
         ContentTemplate="{DataTemplate local:Message}"
         Route="Message" />
    
    <ShellContent
        Title="Teya AI Scribe"
        ContentTemplate="{DataTemplate local:TeyaAI}"
        Route="TeyaAI" />
    <!--Title="{x:Static strings:TeyaAiScribeMobileRes.ChunksMerged}" way to use localizer, later implementation-->

    <ShellContent
        Title="Soap Notes"
        ContentTemplate="{DataTemplate local:TemplatesPage}"
        Route="TemplatesPage" />

    <ShellContent
        Title="Appointments"
        ContentTemplate="{DataTemplate local:AppointmentsDoctor}"
        Route="AppointmentsDoctor" />
    <ShellContent
        Title="PatientHomeView"
        ContentTemplate="{DataTemplate local:PatientHome}"
        Route="PatientHome" />
</Shell>

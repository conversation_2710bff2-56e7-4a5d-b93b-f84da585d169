using System.Globalization;
using TeyaMobileModel.Model;

namespace TeyaAiScribeMobile.Converters
{
    public class RecordingStateToColorConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is RecordingState state)
            {
                return state switch
                {
                    RecordingState.Recording => Colors.Red,
                    RecordingState.Paused => Colors.Orange,
                    RecordingState.Processing => Colors.Blue,
                    RecordingState.Stopped => Colors.Gray,
                    _ => Colors.Gray
                };
            }

            return Colors.Gray;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

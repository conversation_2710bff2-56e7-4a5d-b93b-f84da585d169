﻿using Microsoft.Extensions.Logging;
using CommunityToolkit.Maui;
using Syncfusion.Maui.Core.Hosting;
using Syncfusion.Maui.Popup;
using CommunityToolkit.Maui.Core;
using Microsoft.JSInterop;
using TeyaUIViewModels.ViewModel;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Hosting;
using Syncfusion.Licensing;
using TeyaMobileModel.Model;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization; 
using Microsoft.AspNetCore.Http;
using TeyaMobileViewModel.ViewModel;
using System;
using System.Net.Http;
using Syncfusion.Blazor;


#if ANDROID
using TeyaAiScribeMobile.Platforms;
#elif IOS
using TeyaAiScribeMobile.Platforms;
#endif

namespace TeyaAiScribeMobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();

            builder
                .UseMauiApp<App>()
                .ConfigureSyncfusionCore()
                .UseMauiCommunityToolkit()
                .UseMauiCommunityToolkitMediaElement()
                .UseMauiCommunityToolkitCore()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSans");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("FluentSystemIcons-Filled.ttf", "FluentIcons");
                    fonts.AddFont("MaterialIcons-Regular.ttf", "MaterialIcons");
                })
                .UseMauiCommunityToolkit();

            builder.Logging.ClearProviders();
            builder.Logging.AddDebug();

            using var stream = FileSystem.OpenAppPackageFileAsync("appsettings.json").Result;
            builder.Configuration.AddJsonStream(stream);

            foreach (var setting in builder.Configuration.AsEnumerable())
            {
                if (!string.IsNullOrEmpty(setting.Key) && setting.Value != null)
                {
                    Environment.SetEnvironmentVariable(setting.Key, setting.Value);
                }
            }

            var syncfusionKey = builder.Configuration["SyncfusionKey"];
            if (!string.IsNullOrEmpty(syncfusionKey))
            {
                SyncfusionLicenseProvider.RegisterLicense(syncfusionKey);
            }

            builder.Services.AddSyncfusionBlazor();
            builder.Services.AddLocalization();
            builder.Services.AddSingleton<AppointmentsViewModel>(); 
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<TeyaAI>();
            builder.Services.AddTransient<Message>();
            builder.Services.AddTransient<TemplatesPage>();
            builder.Services.AddTransient<PatientHome>();
            builder.Services.AddTransient<AppointmentsDoctor>();

            builder.Services.AddSingleton<NavigationManager>();
            builder.Services.AddLocalization();

            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
            builder.Services.AddSingleton<ISpeechService, SpeechService>();
            builder.Services.AddSingleton<IProgressNotesService, ProgressNotesService>();
            builder.Services.AddSingleton<PatientService>();
            builder.Services.AddSingleton<AppointmentService>();
            builder.Services.AddSingleton<HttpClient>();
            builder.Services.AddSingleton<ActiveUser>();

            // Register platform-specific services
#if ANDROID
            builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAudioRecorder, TeyaAiScribeMobile.Platforms.Android.Services.AndroidAudioRecorderService>();

#elif IOS
            //builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.ISpeechService, TeyaMobileViewModel.ViewModel.SpeechService>();
                //builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAudioRecorder, TeyaAiScribeMobile.Platforms.AudioRecorderService_iOS>();
                //builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAudioRecorder, TeyaAiScribeMobile.Platforms.AudioRecorderiOS>();
                //builder.Services.AddSingleton<ISpeechService, SpeechService>();
#endif

            return builder.Build();
        }
    }
}
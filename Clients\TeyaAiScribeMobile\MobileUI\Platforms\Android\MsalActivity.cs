﻿using Android.App;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Android.Content;
namespace TeyaAiScribeMobile.Platforms.Android
{
    [Activity(Exported = true)]
    [IntentFilter(new[] { Intent.ActionView },
        Categories = new[] { Intent.CategoryBrowsable, Intent.CategoryDefault },
        DataHost = "auth",
        DataScheme = "msalbbc877ff-1e20-46d5-8a24-f5f4f6426438")]
    public class MsalActivity : BrowserTabActivity
    {
    }
}

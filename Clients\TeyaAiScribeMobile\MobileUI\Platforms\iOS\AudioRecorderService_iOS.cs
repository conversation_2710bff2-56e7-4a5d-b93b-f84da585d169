﻿using AVFoundation;
using Foundation;
using TeyaMobileViewModel.ViewModel;
using Microsoft.Maui.ApplicationModel;
using TeyaAiScribeMobile.Platforms;

[assembly: Dependency(typeof(AudioRecorderService_iOS))]
namespace TeyaAiScribeMobile.Platforms
{
    public class AudioRecorderService_iOS  //IAudioRecorder
    {
        private AVAudioEngine audioEngine;
        private AVAudioFile outputFile;
        private string filePath;

        public async Task StartRecordingAsync()
        {
            audioEngine = new AVAudioEngine();
            filePath = Path.Combine(FileSystem.CacheDirectory, $"teya_{Guid.NewGuid()}.caf");
            var format = audioEngine.InputNode.GetBusOutputFormat(0);
            outputFile = new AVAudioFile(NSUrl.FromFilename(filePath), format.Settings, out _);

            audioEngine.InputNode.InstallTapOnBus(0, 1024, format, (buffer, time) =>
            {
                outputFile.WriteFromBuffer(buffer, out _);

                audioEngine.InputNode.InstallTapOnBus(0, 1024, format, (buffer, time) =>
                {
                    if (buffer.AudioBufferList[0].Data != IntPtr.Zero) // Check if Data is not null
                    {
                        var audioBytes = new byte[(int)buffer.FrameLength * 2]; // mono 16-bit
                        System.Runtime.InteropServices.Marshal.Copy(buffer.AudioBufferList[0].Data, audioBytes, 0, audioBytes.Length);

                        var base64 = Convert.ToBase64String(audioBytes);
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            DependencyService.Get<ISpeechService>().ProcessAudioChunk(base64);
                        });
                    }
                });
            });

            audioEngine.Prepare();
            audioEngine.StartAndReturnError(out NSError? error); // Replace StartAsync with StartAndReturnError  
            if (error != null)
            {
                throw new Exception($"Failed to start audio engine: {error.LocalizedDescription}");
            }
        }

        public Task PauseRecordingAsync()
        {
            audioEngine?.Pause();
            return Task.CompletedTask;
        }

        public Task ResumeRecordingAsync()
        {
            audioEngine?.StartAndReturnError(out _);
            return Task.CompletedTask;
        }

        public Task CancelRecordingAsync()
        {
            audioEngine?.Stop();
            outputFile?.Dispose();
            if (File.Exists(filePath)) File.Delete(filePath);
            return Task.CompletedTask;
        }

        public Task<string?> StopRecordingAsync()
        {
            audioEngine?.Stop();
            outputFile?.Dispose();
            return Task.FromResult<string?>(filePath);
        }
    }
}

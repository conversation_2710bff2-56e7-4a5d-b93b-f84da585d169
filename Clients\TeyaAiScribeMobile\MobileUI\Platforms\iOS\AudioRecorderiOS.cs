﻿using AudioToolbox;
using AVFoundation;
using FFMpegCore;
using Foundation;
using Microsoft.Maui.Controls;
using Plugin.Maui.Audio;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TeyaMobileViewModel.ViewModel;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TeyaAiScribeMobile.Platforms;
using Environment = System.Environment;

[assembly: Dependency(typeof(AudioRecorderiOS))]
namespace TeyaAiScribeMobile.Platforms
{
    public class AudioRecorderiOS //: TeyaMobileViewModel.ViewModel.IAudioRecorder
    {
        private AVAudioRecorder? _audioRecorder;
        private string? _audioFilePath;
        private readonly ILogger<AudioRecorderiOS> _logger;
        private bool _isRecording = false;
        private bool _isPaused = false;
        public event Action<byte[]>? OnAudioDataAvailable;

        public AudioRecorderiOS(ILogger<AudioRecorderiOS> logger)
        {
            _logger = logger;
        }

        public Task CancelRecordingAsync()
        {
            return Task.CompletedTask;
        }

        public bool IsRecording => throw new NotImplementedException();

        public async Task StartRecordingAsync()
        {
            if (_isRecording) return;

            var audioSession = AVAudioSession.SharedInstance();
            var permissionGranted = await RequestRecordPermissionAsync(audioSession);

            if (!permissionGranted)
            {
                _logger.LogError("Microphone permission denied.");
                throw new Exception("Microphone permission denied.");
            }

            try
            {
                audioSession.SetCategory(AVAudioSessionCategory.PlayAndRecord);
                audioSession.SetActive(true);

                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
                _audioFilePath = Path.Combine(documentsPath, $"recording_{DateTime.Now:yyyyMMddHHmmss}.wav");
                var directoryPath = Path.GetDirectoryName(_audioFilePath);
                if (directoryPath == null)
                {
                    _logger.LogError("Failed to get directory path.");
                    throw new Exception("Failed to get directory path.");
                }
                Directory.CreateDirectory(directoryPath);

                var audioSettings = new AudioSettings
                {
                    Format = AudioFormatType.LinearPCM,
                    SampleRate = 16000,
                    NumberChannels = 1,
                    LinearPcmBitDepth = 16,
                    LinearPcmBigEndian = false,
                    LinearPcmFloat = false
                };

                NSError error;
                _audioRecorder = AVAudioRecorder.Create(NSUrl.FromFilename(_audioFilePath), audioSettings, out error);

                if (_audioRecorder == null || error != null)
                {
                    _logger.LogError($"Failed to initialize recorder: {error?.LocalizedDescription}");
                    throw new Exception($"Failed to initialize recorder: {error?.LocalizedDescription}");
                }

                _audioRecorder.PrepareToRecord();
                _audioRecorder.Record();
                _isRecording = true;
                _logger.LogInformation("Recording started successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to start recording: {ex.Message}");
                throw;
            }
        }

        public async Task PauseRecordingAsync()
        {
            if (!_isRecording || _isPaused) return;

            _audioRecorder?.Pause();
            _isPaused = true;
            _logger.LogInformation("Recording paused.");
                }

        public async Task ResumeRecordingAsync()
                {
            if (!_isPaused) return;

            _audioRecorder?.Record();
            _isPaused = false;
            _logger.LogInformation("Recording resumed.");
        }

        public async Task<string> StopRecordingAsync()
        {
            if (!_isRecording) return string.Empty;

            _audioRecorder?.Stop();
            _audioRecorder?.Dispose();
            _audioRecorder = null;

            _isRecording = false;
            _isPaused = false;

            _logger.LogInformation("Recording stopped successfully.");
            return _audioFilePath!;
        }

        private Task<bool> RequestRecordPermissionAsync(AVAudioSession audioSession)
        {
            var tcs = new TaskCompletionSource<bool>();
            audioSession.RequestRecordPermission(granted => tcs.SetResult(granted));
            return tcs.Task;
        }

        public Task StartAsync(AudioRecorderOptions? options = null)
        {
            throw new NotImplementedException();
        }

        public Task StartAsync(string filePath, AudioRecorderOptions? options = null)
        {
            throw new NotImplementedException();
        }

        public Task<IAudioSource> StopAsync()
        {
            throw new NotImplementedException();
        }
    }
}

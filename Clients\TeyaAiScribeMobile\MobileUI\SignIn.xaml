﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TeyaAiScribeMobile.SignIn">

    <ScrollView>
        <VerticalStackLayout
            Padding="30,0"
            Spacing="25">
            <Image
                Source="teyalogo.jpg"
                HeightRequest="185"
                Aspect="AspectFit"
                SemanticProperties.Description="dot net bot in a hovercraft number nine" />

            <Label
                Text="Welcome to Teya Health"
                Style="{StaticResource Headline}"
                SemanticProperties.HeadingLevel="Level1" />

            <Label
                Text="Please Login to unleash our AI"
                Style="{StaticResource SubHeadline}"
                SemanticProperties.HeadingLevel="Level2"
                SemanticProperties.Description="Welcome to dot net Multi platform App UI" />

            <Button
                x:Name="loginBtn"
                Text="Login" 
                Clicked="OnLoginClicked"
                HorizontalOptions="Fill" />
            
            <Label x:Name="NameLabel"/>
            <Label x:Name="AccessToken"/>

            <Button
    x:Name="logoutBtn"
    Text="Logout"
    Clicked="OnLogoutClicked"
    HorizontalOptions="Fill" />

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>

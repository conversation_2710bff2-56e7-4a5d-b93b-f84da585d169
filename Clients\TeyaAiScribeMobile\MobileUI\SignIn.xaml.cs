﻿using Microsoft.Identity.Client;
using System.Xml;

namespace TeyaAiScribeMobile
{
    public partial class SignIn : ContentPage
    {
        private readonly NavigationManager _navigationManager;

        public SignIn(NavigationManager navigationManager)
        {
            InitializeComponent();
            _navigationManager = navigationManager;
        }

        private async void OnLoginClicked(object sender, EventArgs e)
        {
            var publicClientApplication = PublicClientApplicationBuilder
                .Create(EntraConfig.ClientId)
                .WithAuthority(EntraConfig.Authority)
                .WithIosKeychainSecurityGroup(EntraConfig.IOSKeychainSecurityGroup)
                .WithRedirectUri($"msal{EntraConfig.ClientId}://auth")
                .Build();

            try
            {
                // Try silent login
                var accounts = await publicClientApplication.GetAccountsAsync();
                var authResult = await publicClientApplication.AcquireTokenSilent(EntraConfig.Scopes, accounts.FirstOrDefault())
                    .ExecuteAsync();
                var accessToken = authResult.AccessToken;

                await SecureStorage.SetAsync("auth_token", accessToken);
                var token = await SecureStorage.GetAsync("auth_token");

                await SecureStorage.SetAsync("auth_token", accessToken);
                token = await SecureStorage.GetAsync("auth_token");

                NameLabel.Text = $"Welcome, {authResult.Account.Username}";

                Application.Current.MainPage = new AppShell();
                await Shell.Current.GoToAsync("//MainPage");
            }
            catch (MsalUiRequiredException)
            {
                // Silent login failed, fall back to interactive
                try
                {
                    var authResult = await publicClientApplication.AcquireTokenInteractive(EntraConfig.Scopes)
                        .WithParentActivityOrWindow(EntraConfig.ParentWindow)
                        .ExecuteAsync();

                    NameLabel.Text = $"Welcome, {authResult.Account.Username}";
                }
                catch (Exception ex)
                {
                    NameLabel.Text = $"Login failed: {ex.Message}";
                }
            }
            catch (Exception ex)
            {
                NameLabel.Text = $"Error: {ex.Message}";
            }
        }

        private async void OnLogoutClicked(object sender, EventArgs e)
        {
            var publicClientApplication = PublicClientApplicationBuilder
                .Create(EntraConfig.ClientId)
                .WithAuthority(EntraConfig.Authority)
                .WithIosKeychainSecurityGroup(EntraConfig.IOSKeychainSecurityGroup)
                .WithRedirectUri($"msal{EntraConfig.ClientId}://auth")
                .Build();

            try
            {
                // Remove all accounts from MSAL cache
                var accounts = await publicClientApplication.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await publicClientApplication.RemoveAsync(account);
                }

                NameLabel.Text = "You have been logged out.";
            }
            catch (Exception ex)
            {
                NameLabel.Text = $"Logout failed: {ex.Message}";
            }
        }

    }

}

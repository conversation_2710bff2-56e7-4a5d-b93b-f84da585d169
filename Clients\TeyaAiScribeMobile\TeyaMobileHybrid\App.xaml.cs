﻿namespace TeyaMobileHybrid
{
    public partial class App : Application
    {
        public App()
        {
            MainPage = new AppShell();//when sign in page implemented, add signIn page here.
            InitializeComponent();
        }

        //protected override Window CreateWindow(IActivationState? activationState)
        //{
        //    //return new Window(new AppShell()) { Title = "TeyaMobileHybrid" };
        //    return new Window(new SignIn()) { Title = "TeyaMobileHybrid" };
        //}

        public static Page GetRootPage()
        {
            if (Current?.Windows?.Count > 0 && Current.Windows[0].Page is NavigationPage navigationPage)
            {
                return navigationPage.RootPage;
            }
            return null;
        }
    }
}

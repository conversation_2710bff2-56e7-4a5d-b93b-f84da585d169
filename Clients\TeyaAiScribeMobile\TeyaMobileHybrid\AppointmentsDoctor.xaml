<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Scheduler;assembly=Syncfusion.Maui.Scheduler"
             x:Class="TeyaMobileHybrid.AppointmentsDoctor"
             Title="Appointments">

    <ContentPage.Content>
        <Grid>
            <syncfusion:SfScheduler x:Name="doctorScheduler"
                                    ShowDatePickerButton="True"
                                    AllowedViews="Day,Agenda"
                                    AppointmentMapping="{Binding AppointmentMapping}"
                                    AppointmentsSource="{Binding Appointments}"
                                    Tapped="OnSchedulerTapped"/>
        </Grid>
    </ContentPage.Content>
</ContentPage>
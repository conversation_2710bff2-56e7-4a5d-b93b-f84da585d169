﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileHybrid
{
    public static class EntraConfig
    {
        public static string Authority = "https://TeyaAuth.ciamlogin.com/";
        public static string ClientId = "bbc877ff-1e20-46d5-8a24-f5f4f6426438";
        public static string[] Scopes = { "openid", "offline_access" , "api://bbc877ff-1e20-46d5-8a24-f5f4f6426438/access_as_user" };
        public static string IOSKeychainSecurityGroup = "com.microsoft.adalcache";
        public static object? ParentWindow { get; set; }
    }
}

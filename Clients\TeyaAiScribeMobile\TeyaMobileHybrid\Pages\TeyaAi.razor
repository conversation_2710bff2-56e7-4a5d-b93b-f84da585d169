﻿@page "/teyaai"
@inject IJSRuntime JSRuntime

<PageTitle>Teya AI Scribe</PageTitle>

<div style="padding: 20px;">
    <div style="background-color: indianred; border-radius: 20px; padding: 5px; margin: 80px auto 0 auto; width: fit-content;">
        <label style="color: white; font-size: 14px; font-weight: bold;">Teya AI Scribe</label>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <img id="GifAnimation" src="aiwave.gif" style="height: 200px; width: 300px;" />
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <h1 style="font-size: 40px; font-weight: bold;">@TimerLabel</h1>
    </div>

    <div style="display: flex; justify-content: center; gap: 20px; margin-top: 70px;">
        <button @onclick="OnStartClicked" style="background-color: teal; color: white; border-radius: 12px; padding: 15px; font-size: 20px; width: 200px; height: 60px; display: @StartButtonVisible;">Start</button>
        <button @onclick="OnPauseResumeClicked" style="background-color: teal; color: white; border-radius: 10px; padding: 15px; font-size: 22px; width: 160px; height: 60px; display: @PauseResumeButtonVisible;">Pause</button>
        <button @onclick="OnStopClicked" style="background-color: teal; color: white; border-radius: 10px; padding: 15px; font-size: 22px; width: 160px; height: 60px; display: @StopButtonVisible;">Stop</button>
    </div>

    <div style="text-align: right; margin-top: -60px;">
        <img src="lightbulb.png" onclick="OnQuestionMarkClicked()" style="width: 20px; height: 20px; cursor: pointer;" />
    </div>
</div>

<!-- Timer Logic -->
@code {
    private string TimerLabel = "00:00";
    private string StartButtonVisible = "inline-block";
    private string PauseResumeButtonVisible = "none";
    private string StopButtonVisible = "none";

    private int seconds = 0;
    private System.Timers.Timer timer;

    protected override async Task OnInitializedAsync()
    {
        await JSRuntime.InvokeVoidAsync("initializeAudioRecorder", DotNetObjectReference.Create(this));
    }

    private async Task<bool> WaitForScriptLoaded(int timeoutMs = 5000)
    {
        var startTime = DateTime.Now;
        while (!(bool)(await JSRuntime.InvokeAsync<object>("eval", "typeof BlazorAudioRecorder !== 'undefined'")))
        {
            await Task.Delay(100);
            if ((DateTime.Now - startTime).TotalMilliseconds > timeoutMs)
                return false;
        }
        return true;
    }


    private async Task OnStartClicked()
    {
        var status = await Permissions.RequestAsync<Permissions.Microphone>();
        if (status != PermissionStatus.Granted)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Microphone permission is required to record audio.");
            return;
        }

        if (!await WaitForScriptLoaded())
        {
            await JSRuntime.InvokeVoidAsync("alert", "Failed to load AudioRecorder script.");
            return;
        }

        StartButtonVisible = "none";
        PauseResumeButtonVisible = "inline-block";
        StopButtonVisible = "inline-block";

        await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.StartRecord");
        StartTimer();
    }

    private void OnPauseResumeClicked()
    {
        // Implement pause/resume logic here
        // Call JS accordingly
    }

    private void OnStopClicked()
    {
        StartButtonVisible = "inline-block";
        PauseResumeButtonVisible = "none";
        StopButtonVisible = "none";
        StopRecording();
        StopTimer();
    }

    private async void StartRecording()
    {
        await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.StartRecord");
    }

    private async void StopRecording()
    {
        await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.StopRecord", Guid.NewGuid().ToString(), "your-access-token-here");
    }

    private void StartTimer()
    {
        timer = new System.Timers.Timer(1000);
        timer.Elapsed += (sender, e) =>
        {
            seconds++;
            TimerLabel = TimeSpan.FromSeconds(seconds).ToString(@"mm\:ss");
            InvokeAsync(StateHasChanged);
        };
        timer.AutoReset = true;
        timer.Enabled = true;
    }

    private void StopTimer()
    {
        timer?.Stop();
        seconds = 0;
        TimerLabel = "00:00";
    }

    [JSInvokable]
    public void OnAudioDetected(bool isDetected)
    {
        JSRuntime.InvokeVoidAsync("controlGifAnimation", isDetected);
    }

    [JSInvokable]
    public void OnRecordingComplete(string resultId)
    {
        Console.WriteLine($"Recording complete: {resultId}");
    }

    [JSInvokable]
    public void OnRecordingError(string errorMessage)
    {
        Console.WriteLine($"Recording error: {errorMessage}");
    }
}
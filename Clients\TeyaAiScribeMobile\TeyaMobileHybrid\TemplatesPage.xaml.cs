using System;
using System.Collections.Generic;
using System.Text.Json;
using Microsoft.Maui.Controls;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobileHybrid;

[QueryProperty(nameof(PatientId), "patientId")]
[QueryProperty(nameof(OrgId), "orgId")]
[QueryProperty(nameof(PCPId), "pcpId")]
public partial class TemplatesPage : ContentPage
{
    public Guid PatientId { get; set; }
    public Guid OrgId { get; set; }
    public Guid PCPId { get; set; }

    private readonly IProgressNotesService _notesService;
    private Dictionary<string, Dictionary<string, string>> _currentData;

    public TemplatesPage(IProgressNotesService notesService)
    {
        InitializeComponent();
        _notesService = notesService;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await LoadDynamicSoapNotes();
    }

    private async Task LoadDynamicSoapNotes()
    {
        try
        {
            PatientId = Guid.Parse("24E73BD4-1EA8-403C-98CD-DB2DFE6FA1D0");
            OrgId = Guid.Parse("00000000-0000-0000-0000-000000000000");
            var records = await _notesService.GetRecordsByPatientIdAsync(PatientId, OrgId, false);

            if (records == null || records.Count == 0)
            {
                await DisplayAlert("No Data", "No notes found for this patient.", "OK");
                return;
            }

            TemplatesLayout.Children.Clear();

            foreach (var record in records)
            {
                if (record.isEditable == true && record.Notes != null)
                {
                    var json = record.Notes;
                    _currentData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(json);

                    var recordCard = new Frame
                    {
                        CornerRadius = 10,
                        BackgroundColor = Color.FromArgb("#000000"),
                        Padding = new Thickness(5),
                        Margin = new Thickness(0, 3, 0, 3)
                    };

                    var recordContent = new StackLayout { Spacing = 7 };

                    foreach (var section in _currentData)
                    {
                        var sectionContainer = new Frame
                        {
                            CornerRadius = 8,
                            BackgroundColor = Color.FromArgb("#FFFFFF"),
                            Padding = new Thickness(5),
                            Margin = new Thickness(0, 3, 0, 3)
                        };

                        var sectionContent = new StackLayout { Spacing = 2.5 };

                        var sectionHeader = new Label
                        {
                            Text = section.Key,
                            FontAttributes = FontAttributes.Bold,
                            FontSize = 18,
                            TextColor = Colors.Black,
                            Margin = new Thickness(0, 2, 0, 2)
                        };
                        sectionContent.Children.Add(sectionHeader);

                        foreach (var item in section.Value)
                        {
                            var subSectionCard = new Frame
                            {
                                CornerRadius = 6,
                                BackgroundColor = Color.FromArgb("#FFFFFF"),
                                Padding = new Thickness(5),
                                Margin = new Thickness(0, 2.5, 0, 2.5)
                            };

                            var subSectionContent = new StackLayout { Spacing = 2.5 };

                            var subSectionTitle = new Label
                            {
                                Text = item.Key,
                                FontAttributes = FontAttributes.Bold,
                                FontSize = 14,
                                TextColor = Colors.Black
                            };
                            subSectionContent.Children.Add(subSectionTitle);

                            var subSectionValue = new Editor
                            {
                                Text = item.Value,
                                BackgroundColor = Colors.White,
                                TextColor = Colors.Black,
                                IsReadOnly = true,
                                HeightRequest = 100,
                                Margin = new Thickness(0, 5, 0, 5),
                                AutoSize = EditorAutoSizeOption.Disabled
                            };

                            subSectionValue.BackgroundColor = Colors.White;
                            subSectionValue.Margin = new Thickness(5);
                            subSectionValue.Shadow = new Shadow
                            {
                                Brush = Colors.Teal,
                                Radius = 5,
                                Offset = new Point(0, 2),
                                Opacity = 0.5F
                            };
                            subSectionContent.Children.Add(subSectionValue);

                            var editButton = new Button
                            {
                                Text = "Edit",
                                BackgroundColor = Colors.Transparent,
                                TextColor = Colors.DarkRed,
                                FontSize = 12,
                                Margin = new Thickness(0, 5, 0, 0)
                            };

                            editButton.Clicked += async (sender, args) =>
                            {
                                subSectionValue.IsReadOnly = false;

                                var saveButton = new Button
                                {
                                    Text = "Save",
                                    BackgroundColor = Colors.Transparent,
                                    TextColor = Colors.Black,
                                    FontSize = 12,
                                    Margin = new Thickness(0, 5, 0, 0)
                                };

                                saveButton.Clicked += async (saveSender, saveArgs) =>
                                {
                                    subSectionValue.IsReadOnly = true;

                                    _currentData[section.Key][item.Key] = subSectionValue.Text;

                                    var updatedJson = JsonSerializer.Serialize(_currentData);

                                    var updatedRecord = new Record
                                    {
                                        Id = record.Id,
                                        Notes = updatedJson,
                                        isEditable = true
                                    };

                                    await _notesService.SaveRecordAsync(updatedRecord, OrgId, false);

                                    subSectionContent.Children.Remove(saveButton);
                                };

                                subSectionContent.Children.Add(saveButton);
                            };

                            subSectionContent.Children.Add(editButton);
                            subSectionCard.Content = subSectionContent;
                            sectionContent.Children.Add(subSectionCard);
                        }

                        sectionContainer.Content = sectionContent;
                        recordContent.Children.Add(sectionContainer);
                    }

                    recordCard.Content = recordContent;
                    TemplatesLayout.Children.Add(recordCard);
                }
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to load notes: {ex.Message}", "OK");
        }
    }
}
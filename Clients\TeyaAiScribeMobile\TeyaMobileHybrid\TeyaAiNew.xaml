<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TeyaMobileHybrid.TeyaAiNew"
             Title="TeyaAiNew">
    <ContentPage.Resources>
        <Style TargetType="Button" Class="rounded-button">
            <Setter Property="CornerRadius" Value="30"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="TextColor" Value="White"/>
            <Setter Property="FontSize" Value="20"/>
        </Style>
    </ContentPage.Resources>
    <Grid RowDefinitions="Auto, Auto, Auto, Auto, Auto"
          ColumnDefinitions="*, *"
          Padding="20">
        <Frame Grid.Row="0"
       Grid.ColumnSpan="2"
       BackgroundColor="IndianRed"
       CornerRadius="20"
       Padding="5"
       Margin="70,80,70,0"
       HeightRequest="35">
            <Label x:Name="StatusLabel"
           Text="Teya AI Scribe"
           TextColor="White"
           FontAttributes="Bold"
           FontSize="14"
           HorizontalTextAlignment="Center"/>
        </Frame>

        <Image Grid.Row="1"
               Grid.ColumnSpan="2"
               x:Name="GifAnimation"
               Source="aiwave.gif"
               IsAnimationPlaying="False"
               HorizontalOptions="Center"
               VerticalOptions="Center"
               HeightRequest="200"
               WidthRequest="300"
               Margin="0,0,0,5"/>

        <Label Grid.Row="2"
               Grid.ColumnSpan="2"
               x:Name="TimerLabel"
               Text="00:00"
               FontSize="40"
               FontAttributes="Bold"
               TextColor="Black"
               HorizontalTextAlignment="Center"
               Margin="0,0,0,70"/>

        <StackLayout Grid.Row="3"
                     Grid.ColumnSpan="2"
                     Orientation="Horizontal"
                     HorizontalOptions="Center"
                     Spacing="20">
            <Button x:Name="StartButton"
                    Text="Start"
                    Style="{StaticResource rounded-button}"
                    BackgroundColor="Teal"
                    Clicked="OnStartClicked"
                    ImageSource="aimic.png"
                    CornerRadius="12"
                    ContentLayout="Left,7"
                    WidthRequest="200"
                    HeightRequest="60"
                    IsVisible="True"/>

            <Button x:Name="PauseResumeButton"
                    Text="Pause"
                    Style="{StaticResource rounded-button}"
                    BackgroundColor="Teal"
                    Clicked="OnPauseResumeClicked"
                    FontSize="22"
                    ImageSource="aipause.png"
                    CornerRadius="10"
                    ContentLayout="Left,10"
                    WidthRequest="160"
                    HeightRequest="60"
                    IsVisible="False"/>

            <Button x:Name="StopButton"
                    Text="Stop"
                    Style="{StaticResource rounded-button}"
                    CornerRadius="10"
                    BackgroundColor="Teal"
                    Clicked="OnStopClicked"
                    FontSize="22"
                    ImageSource="aistop.png"
                    ContentLayout="Left,10"
                    WidthRequest="160"
                    HeightRequest="60"
                    IsVisible="False"/>

            <Button x:Name="TranscribeButton"
                 Text="Transcribe"
                 Clicked="OnTranscribeClicked"
                 IsVisible="False"
                 Style="{StaticResource PrimaryButton}" />
        </StackLayout>

        <ImageButton x:Name="QuestionMarkIcon" 
                     Source="lightbulb.png" 
                     Clicked="OnQuestionMarkClicked" 
                     HorizontalOptions="End" 
                     WidthRequest="10" 
                     HeightRequest="10"
                     Margin="0,0,140,60"/>

       
    </Grid>
</ContentPage>
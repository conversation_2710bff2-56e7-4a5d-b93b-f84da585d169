using FFMpegCore;
using Microsoft.Maui.Controls;
using Plugin.Maui.Audio;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobileHybrid;

public partial class TeyaAiNew : ContentPage, INotifyPropertyChanged
{
    private readonly ISpeechService _speechService;
    private readonly TeyaMobileViewModel.ViewModel.IAudioRecorder _audioRecorder;
    private readonly IAudioManager _audioManager;
    private bool _transcriptionComplete = false;
    private readonly PatientService _patientService;
    private bool _isRecording = false;
    private bool _isPaused = false;
    private Stopwatch _stopwatch;
    private TimeSpan _pausedTime;
    private string _currentFilePath;

    public TeyaAiNew(ISpeechService speechService, TeyaMobileViewModel.ViewModel.IAudioRecorder audioRecorder, IAudioManager audioManager, PatientService patientService)
    {
        InitializeComponent();
        BindingContext = this;
        _speechService = speechService;
        _audioRecorder = audioRecorder;
        _audioManager = audioManager;
        _patientService = patientService;
        _stopwatch = new Stopwatch();

        var selectedPatient = _patientService.SelectedPatient;
        BindingContext = selectedPatient;
    }

    private async void OnStartClicked(object sender, EventArgs e)
    {
        if (await Permissions.RequestAsync<Permissions.Microphone>() != PermissionStatus.Granted)
        {
            await DisplayAlert("Permission Denied", "Microphone access is required.", "OK");
            return;
        }

        var recordId = Guid.NewGuid();
        await _speechService.StartTranscriptionAsync(recordId);

        _isRecording = true;
        _isPaused = false;

        StatusLabel.Text = "Recording...";
        GifAnimation.IsVisible = true;
        GifAnimation.IsAnimationPlaying = true;

        StartButton.IsVisible = false;
        PauseResumeButton.IsVisible = true;
        StopButton.IsVisible = true;

        _stopwatch.Restart();

        Dispatcher.StartTimer(TimeSpan.FromSeconds(1), () =>
        {
            if (_isRecording && !_isPaused)
            {
                TimerLabel.Text = _stopwatch.Elapsed.ToString(@"hh\:mm\:ss");
            }
            return _isRecording;
        });

        try
        {
            await _audioRecorder.StartRecordingAsync();
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Recording failed: {ex.Message}", "OK");
            ResetUI();
        }
    }

    private async void OnPauseResumeClicked(object sender, EventArgs e)
    {
        _isPaused = !_isPaused;

        if (_isPaused)
        {
            PauseResumeButton.Text = "Resume";
            PauseResumeButton.ImageSource = "aiplay.png";
            GifAnimation.IsAnimationPlaying = false;
            _pausedTime = _stopwatch.Elapsed;
            _stopwatch.Stop();
            await _audioRecorder.PauseRecordingAsync();
        }
        else
        {
            PauseResumeButton.Text = "Pause";
            PauseResumeButton.ImageSource = "aipause.png";
            GifAnimation.IsAnimationPlaying = true;
            _stopwatch.Start();
            await _audioRecorder.ResumeRecordingAsync();
        }
    }

    private async void OnStopClicked(object sender, EventArgs e)
    {
        _isRecording = false;
        _isPaused = false;
        _stopwatch.Reset();

        StatusLabel.Text = "Teya AI Scribe";
        GifAnimation.IsAnimationPlaying = false;
        TimerLabel.Text = "00:00";

        StartButton.IsVisible = true;
        PauseResumeButton.IsVisible = false;
        StopButton.IsVisible = false;

        try
        {
            var filePath = await _audioRecorder.StopRecordingAsync();
            _currentFilePath = filePath;

            //await _speechService.UploadAudioAsync(filePath, _speechService.GetCurrentRecordingId());
            await DisplayAlert("Audio uploaded", "Audio uploaded successfully.", "OK");

            await UploadAndTranscribeAsync(filePath);
            _transcriptionComplete = true;
            TranscribeButton.IsVisible = true;
            await DisplayAlert("Transcription", "Transcription completed successfully.", "OK");
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to stop recording: {ex.Message}", "OK");
        }
    }

    private async void OnTranscribeClicked(object sender, EventArgs e)
    {
        Guid patientId = Guid.Parse("24E73BD4-1EA8-403C-98CD-DB2DFE6FA1D0"); //_patientService.SelectedPatient.PatientId;
        Guid orgId = Guid.Parse("00000000-0000-0000-0000-000000000000"); //Guid.Parse(_patientService.SelectedPatient.PCPID); // or _user.id if from auth

        await Shell.Current.GoToAsync($"//TemplatesPage?patientId={patientId}&orgId={orgId}");
    }


    private async Task PlayAudioAsync(string filePath)
    {
        if (!File.Exists(filePath))
        {
            await DisplayAlert("Missing File", "File doesn't exist.", "OK");
            return;
        }

        using var stream = File.OpenRead(filePath);
        var player = _audioManager.CreatePlayer(stream);
        player.Play();
    }

    private string ConvertToWav(string inputPath)
    {
        try
        {
            var outputPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), $"{Path.GetFileNameWithoutExtension(inputPath)}.wav");

            if (File.Exists(outputPath))
                File.Delete(outputPath);

            FFMpegArguments
                .FromFileInput(inputPath)
                .OutputToFile(outputPath, true, options => options
                    .WithAudioCodec("pcm_s16le")
                    .ForceFormat("wav")
                    .WithCustomArgument("-y"))
                .ProcessSynchronously();

            return outputPath;
        }
        catch
        {
            return null;
        }
    }

    private async Task UploadAndTranscribeAsync(string filePath)
    {
        try
        {
            var recordId = _speechService.GetCurrentRecordingId();
            var patientId = recordId; //_patientService.SelectedPatient.PatientId;
            var visitType = "Regular"; //_patientService.SelectedPatient.VisitType;

            //await _speechService.StopTranscriptionAsync(recordId, patientId, visitType);

            await DisplayAlert("Done", "Audio transcribed and uploaded successfully.", "OK");
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to transcribe: {ex.Message}", "OK");
        }
    }

    private void ResetUI()
    {
        _isRecording = false;
        _isPaused = false;
        _stopwatch.Reset();

        StatusLabel.Text = "Teya AI Scribe";
        GifAnimation.IsAnimationPlaying = false;
        TimerLabel.Text = "00:00";

        StartButton.IsVisible = true;
        PauseResumeButton.IsVisible = false;
        StopButton.IsVisible = false;
    }

    public event PropertyChangedEventHandler PropertyChanged;
    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null!)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    private void OnQuestionMarkClicked(object sender, EventArgs e)
    {
        var testPlayer = _audioManager.CreatePlayer("TestMsg.mp3");
        testPlayer.Play();
    }
}

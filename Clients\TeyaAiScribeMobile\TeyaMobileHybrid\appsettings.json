{"EncounterNotesURLSpeech": "http://10.0.2.2/EncounterNotesService/Speech", "EncounterNotesURLUpload": "http://10.0.2.2/EncounterNotesService/Speech/upload", "EncounterNotesURL": "http://10.0.2.2/EncounterNotesService", "AZURE_BLOB_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=teyarecordingsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AZURE_BLOB_CONTAINER_NAME": "audiofiles", "AZURE_SPEECH_API_KEY": "91SxUNRBf4Q5jx9PUNcFCAXwDjymNH7yoqudOgaYX9vYtWGP0JIZJQQJ99ALACYeBjFXJ3w3AAAYACOGwBTm", "AZURE_REGION": "eastus", "AZURE_LANGUAGE": "en-US", "AudioUrl": "https://teyarecording.blob.core.windows.net/audiofiles", "AUTH_CLIENT_ID": "e21369d6-92b3-446b-b981-0291bcb29b1b", "AUTH_AUTHORITY": "https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597/v2.0", "AUTH_CLIENT_SECRET": "****************************************", "AUTH_RESPONSE_TYPE": "code", "AUTH_SAVE_TOKENS": "true", "AUTH_CALLBACK_PATH": "/signin-oidc", "AUTH_SCOPE_0": "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user", "AUTH_SCOPE_1": "openid", "AUTH_SCOPE_2": "profile", "AUTH_SCOPE_3": "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user", "AUTH_SCOPE_4": "https://graph.microsoft.com/.default", "AzureBusConnectionString": "Endpoint=sb://teyahealth-dev.servicebus.windows.net/;SharedAccessKeyName=Task;SharedAccessKey=ZEy2iTOVqSjOxSEWSyiqN/8cRPDS+ASbOYB2DM=;EntityPath=tasks", "TopicName": "tasks", "PrimaryDomain": "TeyaHealthDevAuth.onmicrosoft.com", "ROLE_SEARCH_URL": "http://localhost/MemberServiceApi/api/Roles/search", "ORGANIZATIONS_API_URL": "http://localhost/MemberServiceApi/api/Organizations/search", "AUTH_LOGOUT_URI": "https://TeyaHealthDevAuth.ciamlogin.com/0319-4ae7-8ed7-47b79597/oauth2/v2.0/logout", "AUTH_POST_LOGOUT_URI": "https://localhost:7170/", "RxNormBrandListUrl": "https://rxnav.nlm.nih.gov/REST/Prescribe/allconcepts.json?tty=BN", "RxNormBrandSBDListUrl": "https://rxnav.nlm.nih.gov/REST/Prescribe/drugs.json", "PatientDataurl": "http://localhost/MemberServiceApi/Patient", "CurrentMedicationUrl": "http://localhost/EncounterNotesService/api/CurrentMedication", "RegistrationUrl": "http://localhost/MemberServiceApi/api/Registration/registration", "OrganizationsUrl": "http://localhost/MemberServiceApi/api/Organizations", "FacilitiesUrl": "http://localhost/MemberServiceApi/api/Facility", "FACILITY_URL": "http://localhost/MemberServiceApi/api/Facility", "USER_THEME_URL": "http://localhost/MemberServiceApi/api/UserTheme", "DELETE_USER_THEME_URL": "http://localhost/MemberServiceApi/api/UserTheme", "PAGE_ROLE_MAPPING_URL": "http://localhost/MemberServiceApi/api/PageRoleMapping", "DELETE_PAGE_ROLE_MAPPING_URL": "http://localhost/MemberServiceApi/api/PageRoleMapping", "_registrationOrganizationUrl": "http://localhost/MemberServiceApi/api/Organizations", "RolesUrl": "http://localhost/MemberServiceApi/api/Roles", "MembersUrl": "http://localhost/MemberServiceApi/api/Products/{productId}/members", "ProductsUrl": "http://localhost/MemberServiceApi/api/Products", "UpdateAccessUrl": "http://localhost/MemberServiceApi/api/Products/updateAccess", "LicenseUrl": "http://localhost/MemberServiceApi/api/Licenses", "UpdateLicenseAccessUrl": "http://localhost/MemberServiceApi/api/Licenses/updateAccess", "AppointmentsUrl": "http://localhost/Appointments/api/Appointments", "RecordsUrl": "http://localhost/EncounterNotesService/api/Records", "AppointmentRegistration": "http://localhost/Appointments/api/Appointments/appointmentRecord", "MemberRegistration": "http://localhost/MemberServiceApi/api/Registration", "SyncfusionKey": "MzU3MTQ3NUAzMjM3MmUzMDJlMzBVaUhlNTJjTEcwd00vVGtWWnh1ZmVxSWlMa3IwUlcvM2xlQzF3cklPUXFrPQ==", "RedisConnectionString": "TeyaHealthRedisCache.redis.cache.windows.net:6380,password=lyvUJ8XxAnKfamb8vvRKfl2tSwZreIrRAAzCaPaM4w0=,ssl=True,abortConnect=False", "TasksUrl": "http://localhost/PracticeApi/api/Practice", "OfficeVisit_appointments": "http://localhost/Appointments/api/Appointments/user", "officeVisit_members": "http://localhost/MemberServiceApi/api/Registration", "VisitTypesUrl": "http://localhost/MemberServiceApi/api/VisitType", "VisitStatusUrl": "http://localhost/MemberServiceApi/api/VisitStatus", "ProductId": "http://localhost/MemberServiceApi/api/Products", "TasksRegistration": "http://localhost/PracticeApi/api/Practice/registration", "PeopleUrl": "http://localhost/PracticeApi/api/People", "TemplatesUrl": "http://localhost/EncounterNotesService/api/Templates", "TemplatesInsertion": "http://localhost/EncounterNotesService/api/Templates/Templates", "PredefinedTemplatesUrl": "http://localhost/EncounterNotesService/api/PredefinedTemplates", "PredefinedTemplatesInsertion": "http://localhost/EncounterNotesService/api/PredefinedTemplates/Templates"}
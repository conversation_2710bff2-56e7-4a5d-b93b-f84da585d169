﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class ThemeModel : IModel
    {
        public ThemePalette PaletteLight { get; set; } = new();
        public ThemePalette PaletteDark { get; set; } = new();
        public ThemeLayoutProperties LayoutProperties { get; set; } = new();
    }
}

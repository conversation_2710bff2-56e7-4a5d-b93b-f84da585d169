﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class ThemePalette : IModel
    {
        public string Primary { get; set; }
        public string Secondary { get; set; }
        public string AppbarBackground { get; set; }
        public string Background { get; set; }
        public string Surface { get; set; }
        public string DrawerBackground { get; set; }
        public string TextPrimary { get; set; }
        public string TextSecondary { get; set; }
        public string ActionDefault { get; set; }
        public string ActionDisabled { get; set; }
        public string ActionDisabledBackground { get; set; }
    }
}


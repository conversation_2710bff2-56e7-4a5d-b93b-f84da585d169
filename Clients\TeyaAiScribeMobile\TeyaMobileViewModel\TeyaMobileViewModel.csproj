﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FFMpegCore" Version="5.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.Analyzers" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.3" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
		<PackageReference Include="Carbon.Redis" Version="2.8.2" />
		<PackageReference Include="DotNetEnv" Version="3.1.1" />
		<PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.43.0" />
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.6" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
		<PackageReference Include="Microsoft.Graph" Version="5.68.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Plugin.Maui.Audio" Version="3.1.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.16" />
		<PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.58" />
		<PackageReference Include="Syncfusion.Blazor.Lists" Version="27.1.58" />
		<PackageReference Include="Syncfusion.Blazor.Popups" Version="27.1.58" />
		<PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.58" />
		<PackageReference Include="System.IO.FileSystem" Version="4.3.0" />
		<PackageReference Include="System.Text.Json" Version="9.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\TeyaMobileModel\TeyaMobileModel.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.Designer.cs">
	    <DependentUpon>TeyaUIViewModelsStrings.resx</DependentUpon>
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	  </Compile>
	  <Compile Update="TeyaUIViewModelResource\TeyaUIViewModelsResource.Designer.cs">
	    <DependentUpon>TeyaUIViewModelsResource.resx</DependentUpon>
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.resx">
	    <SubType>Designer</SubType>
	    <LastGenOutput>TeyaUIViewModelsStrings.Designer.cs</LastGenOutput>
	    <Generator>PublicResXFileCodeGenerator</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Update="TeyaUIViewModelResource\TeyaUIViewModelsResource.resx">
	    <LastGenOutput>TeyaUIViewModelsResource.Designer.cs</LastGenOutput>
	    <Generator>PublicResXFileCodeGenerator</Generator>
	  </EmbeddedResource>
	</ItemGroup>

</Project>

﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Azure.Core;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;
using TeyaMobileViewModel.TeyaUIViewModelResources;
using System.Net.Http.Headers;

namespace TeyaMobileViewModel.ViewModel
{
    public class AppointmentService : IAppointmentService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _AppointmentsUrl;
        private readonly IConfiguration _configuration;

        public AppointmentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer)
        {
            _httpClient = httpClient;
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _AppointmentsUrl = "http://10.0.2.2/Appointments/api/Appointments";  // _configuration[AppointmentsURL]  http://172.27.96.1/Appointmnets/api/Appointments _configuration[AppointmentsURL];
        }

        public async Task<List<Appointment>> GetAllAppointmentsAsync()
        {
            try
            {
                // Properly await the token retrieval
                var token = await SecureStorage.GetAsync("auth_token");

                if (string.IsNullOrEmpty(token))
                {
                    throw new InvalidOperationException("Authentication token not found");
                }

                var request = new HttpRequestMessage(HttpMethod.Get, _AppointmentsUrl);
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

                var response = await _httpClient.SendAsync(request);

                response.EnsureSuccessStatusCode(); // Throws exception for non-success codes

                var appointments = await response.Content.ReadFromJsonAsync<List<Appointment>>();
                return appointments ?? new List<Appointment>();
            }
            catch (HttpRequestException ex)
            {
                // Log the error if needed
                Console.WriteLine($"HTTP request failed: {ex.Message}");
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"], ex);
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                Console.WriteLine($"Error retrieving appointments: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Retrieves a list of appointments for a specific date.
        /// </summary>
        /// <param name="date">The date for which appointments are to be retrieved.</param>
        /// <returns>A list of appointments.</returns>
        public async Task<List<Appointment>> GetAppointmentsAsync(DateTime date)
        {
            var dateUrl = $"{_AppointmentsUrl}/api/Appointments/{date:yyyy-MM-dd}";
            var response = await _httpClient.GetAsync(dateUrl);

            if (response.IsSuccessStatusCode)
            {
                var appointments = await response.Content.ReadFromJsonAsync<List<Appointment>>();
                return appointments ?? new List<Appointment>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Creates new appointments by sending a request to the appointment registration API.
        /// </summary>
        /// <param name="appointments">A list of appointments to be created.</param>
        public async Task CreateAppointmentsAsync(List<Appointment> appointments)
        {
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/appointmentRecord";
            var bodyContent = JsonSerializer.Serialize(appointments);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(apiUrl, content);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Updates an existing appointment.
        /// </summary>
        /// <param name="appointment">The appointment object containing updated details.</param>
        public async Task UpdateAppointmentAsync(Appointment appointment)
        {
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointment.Id}";
            var bodyContent = JsonSerializer.Serialize(appointment);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync(apiUrl, content);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Deletes an appointment by its unique identifier.
        /// </summary>
        /// <param name="appointmentId">The unique identifier of the appointment to be deleted.</param>
        public async Task DeleteAppointmentAsync(Guid appointmentId)
        {
            try
            {
                var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointmentId}";
                var response = await _httpClient.DeleteAsync(apiUrl);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }
    }
}
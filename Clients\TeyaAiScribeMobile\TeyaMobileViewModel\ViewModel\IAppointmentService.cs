﻿
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IAppointmentService
    {
        Task<List<Appointment>> GetAllAppointmentsAsync();
        Task<List<Appointment>> GetAppointmentsAsync(DateTime date);
        Task CreateAppointmentsAsync(List<Appointment> appointments);
        Task DeleteAppointmentAsync(Guid appointmentId);
        Task UpdateAppointmentAsync(Appointment appointment);
    }

}

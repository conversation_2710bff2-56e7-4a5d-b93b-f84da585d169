﻿using System.Text;
using Newtonsoft.Json;
using Microsoft.CognitiveServices.Speech.Audio;
using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Transcription;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Azure.Storage.Blobs;
using System.IO;
using Azure.Storage.Blobs.Models;
using System.Net.Http.Headers;
using System;
using System.Collections.Concurrent;

namespace TeyaMobileViewModel.ViewModel
{
    public class SpeechService : ISpeechService, IDisposable
    {
        private readonly HttpClient _httpClient;
        private SpeechRecognizer? _recognizer;
        private SpeechConfig? _speechConfig;
        private readonly ILogger _logger;
        private readonly IStringLocalizer _localizer;
        private readonly IConfiguration _configuration;
        private readonly ActiveUser _user;
        private ConversationTranscriber _conversationTranscriber;
        private PushAudioInputStream _pushStream;
        private readonly List<TeyaMobileModel.Model.Speech> _collectedSpeeches = new();
        private readonly List<WordTiming> _tempWordTimings = new();
        private Guid _currentRecordingId;
        private bool _disposed = false;
        private readonly object _pushStreamLock = new();
        private string _continuousRecognitionText = string.Empty;
        public string TotalTranscribed { get; set; } = string.Empty;
        public string SpeechData { get; set; } = string.Empty;
        public event EventHandler<string>? OnPartialTranscription;
        public event EventHandler<string>? OnFinalTranscription;
        public event EventHandler<Exception>? OnError;

        public SpeechService(HttpClient httpClient,
                             ILogger<SpeechService> logger,
                             IStringLocalizer<TeyaUIViewModelsStrings> localizer,
                             IConfiguration configuration
                             //, ActiveUser user
                             )
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            //_user = user ?? throw new ArgumentNullException(nameof(user));
        }

        public Guid GetCurrentRecordingId() => _currentRecordingId;

        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            if (_pushStream != null)
            {
                try
                {
                    byte[] audioBytes = Convert.FromBase64String(base64AudioChunk);
                    lock (_pushStreamLock)
                    {
                        _pushStream.Write(audioBytes);
                    }
                    //_pushStream.Write(audioBytes);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing audio chunk");
                }
            }
        }

        public async Task StartTranscriptionAsync(Guid id)
        {
            _currentRecordingId = id;
            _collectedSpeeches.Clear();
            _tempWordTimings.Clear();
            TotalTranscribed = string.Empty;
            SpeechData = string.Empty;

            var speechConfig = InitializeSpeechConfig();
            if (speechConfig == null)
            {
                _logger.LogError("Failed to initialize SpeechConfig.");
                throw new InvalidOperationException("SpeechConfig initialization failed.");
            }
            _pushStream = AudioInputStream.CreatePushStream(AudioStreamFormat.GetWaveFormatPCM(16000, 16, 1));
            var audioConfig = AudioConfig.FromStreamInput(_pushStream);

            _conversationTranscriber = new ConversationTranscriber(speechConfig, audioConfig);
            var stopRecognition = new TaskCompletionSource<int>(TaskCreationOptions.RunContinuationsAsynchronously);

            RegisterTranscriberEvents(stopRecognition);
            await _conversationTranscriber.StartTranscribingAsync();
        }

        private SpeechConfig InitializeSpeechConfig()
        {
            var apiKey = _configuration["AZURE_SPEECH_API_KEY"];
            var region = _configuration["AZURE_REGION"];
            var language = _configuration["AZURE_LANGUAGE"] ?? "en-US";

            if (string.IsNullOrWhiteSpace(apiKey) || string.IsNullOrWhiteSpace(region))
            {
                _logger.LogError("Azure Speech API key or region is missing in configuration.");
                return null;
            }

            var config = SpeechConfig.FromSubscription(apiKey, region);
            config.SpeechRecognitionLanguage = language;
            config.SetProperty(PropertyId.SpeechServiceResponse_DiarizeIntermediateResults, "true");
            return config;
        }

        private void RegisterTranscriberEvents(TaskCompletionSource<int> stopRecognition)
        {
            if (_conversationTranscriber == null) return;

            _conversationTranscriber.Transcribed += (s, e) => HandleTranscribedEvent(e);
            _conversationTranscriber.Canceled += (s, e) => stopRecognition.TrySetResult(0);
            _conversationTranscriber.SessionStopped += async (s, e) => await HandleSessionStoppedEvent(stopRecognition);
        }

        private void HandleTranscribedEvent(ConversationTranscriptionEventArgs e)
        {
            try
            {
                if (e.Result.Reason == ResultReason.RecognizedSpeech)
                {
                    TotalTranscribed += $"\n{e.Result.SpeakerId} : {e.Result.Text}";
                    SpeechData += e.Result.Text;
                    var wordTiming = new WordTiming
                    {
                        Word = e.Result.Text,
                        StartTime = TimeSpan.FromTicks(e.Result.OffsetInTicks).TotalSeconds,
                        EndTime = TimeSpan.FromTicks(e.Result.OffsetInTicks + e.Result.Duration.Ticks).TotalSeconds
                    };
                    _tempWordTimings.Add(wordTiming);
                }
                else if (e.Result.Reason == ResultReason.NoMatch)
                {
                    _logger.LogWarning(_localizer["SpeechNotRecognized"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling Transcribed event");
            }
        }

        private async Task HandleSessionStoppedEvent(TaskCompletionSource<int> stopRecognition)
        {
            stopRecognition.TrySetResult(0);
            _collectedSpeeches.Add(new TeyaMobileModel.Model.Speech
            {
                Result = SpeechData,
                Timestamps = new List<WordTiming>(_tempWordTimings),
                TranscribedData = TotalTranscribed
            });
        }

        public async Task StopTranscriptionAsync(Guid id, Guid patientId, string visitType, Guid? orgid, bool sub)
        {
            if (_conversationTranscriber != null)
            {
                await _conversationTranscriber.StopTranscribingAsync();
                await PostTranscriptionsAsync(id, patientId, visitType, null, false);
            }
            ClosePushStream();
        }

        private void ClosePushStream()
        {
            lock (_pushStreamLock)
            {
                _pushStream?.Close();
                _pushStream = null;
            }
        }

        public async Task UploadAudioToBackendAsync(string filePath)
        {
            var backendUrl = _configuration["EncounterNotesURLUpload"]; // e.g., "http://***************:5000/api/audio/upload"
            if (string.IsNullOrEmpty(backendUrl) || !File.Exists(filePath))
                throw new InvalidOperationException("Invalid upload URL or file path.");

            using var httpClient = new HttpClient();

            using var form = new MultipartFormDataContent();
            using var fileStream = File.OpenRead(filePath);
            var fileContent = new StreamContent(fileStream);
            fileContent.Headers.ContentType = new MediaTypeHeaderValue("audio/wav");

            form.Add(fileContent, "audioFile", Path.GetFileName(filePath));

            var response = await httpClient.PostAsync(backendUrl, form);
            response.EnsureSuccessStatusCode();
        }

        public async Task PostTranscriptionsAsync(Guid id, Guid patientId, string visitType, Guid? orgId, bool subscription)
        {
            if (_collectedSpeeches.Count > 0)
            {
                try
                {
                    var speechApiBaseUrl = _configuration["EncounterNotesURLSpeech"];
                    if (string.IsNullOrWhiteSpace(speechApiBaseUrl) || orgId == null)
                    {
                        _logger.LogError("EncounterNotesURLSpeech or orgId is missing in configuration.");
                        return;
                    }

                    // Append orgId and subscription to the URL as required by the backend route
                    var uri = new Uri($"{speechApiBaseUrl.TrimEnd('/')}/{orgId.Value}/{subscription}");

                    var speeches = new SpeechRequest
                    {
                        Id = id,
                        PCPId = id,
                        PatientId = patientId,
                        OrganizationId = orgId.Value,
                        Speeches = _collectedSpeeches,
                        VisitType = visitType,
                    };

                    var bodyContent = System.Text.Json.JsonSerializer.Serialize(speeches);
                    var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                    var requestMessage = new HttpRequestMessage(HttpMethod.Post, uri)
                    {
                        Content = content
                    };

                    using var httpClient = new HttpClient();
                    var response = await httpClient.PostAsync(uri, content);

                    if (response.IsSuccessStatusCode)
                    {
                        var responseBody = await response.Content.ReadAsStringAsync();
                        dynamic result = JsonConvert.DeserializeObject(responseBody);
                        if (result != null && result.firstRecordId != null)
                            _currentRecordingId = result.firstRecordId;
                    }
                    else
                    {
                        _logger.LogError("Failed to post transcriptions: {StatusCode}", response.StatusCode);
                    }

                    _collectedSpeeches.Clear();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error posting transcriptions");
                }
            }
        }

        public async Task StartContinuousRecognitionAsync()
        {
            try
            {
                _logger.LogInformation("Starting continuous speech recognition");

                _speechConfig = InitializeSpeechConfig();
                using var audioConfig = AudioConfig.FromDefaultMicrophoneInput();
                _recognizer = new SpeechRecognizer(_speechConfig, audioConfig);

                _recognizer.Recognizing += (s, e) =>
                {
                    var partialText = e.Result.Text;
                    _logger.LogDebug("Partial transcription: {Text}", partialText);
                    OnPartialTranscription?.Invoke(this, partialText);
                };

                _recognizer.Recognized += (s, e) =>
                {
                    if (e.Result.Reason == ResultReason.RecognizedSpeech)
                    {
                        var finalText = e.Result.Text;
                        _logger.LogDebug("Final transcription: {Text}", finalText);
                        OnFinalTranscription?.Invoke(this, finalText);

                        if (!string.IsNullOrWhiteSpace(finalText))
                        {
                            _continuousRecognitionText += finalText + " ";
                            _collectedSpeeches.Add(new TeyaMobileModel.Model.Speech
                            {
                                Result = finalText,
                                Timestamps = new List<WordTiming>(), // You can fill this if you have word timings
                                TranscribedData = finalText
                            });
                        }
                    }
                };

                _recognizer.Canceled += (s, e) =>
                {
                    if (e.Reason == CancellationReason.Error)
                    {
                        var error = new Exception($"Speech recognition canceled: {e.ErrorCode} - {e.ErrorDetails}");
                        _logger.LogError(error, "Speech recognition canceled");
                        OnError?.Invoke(this, error);
                    }
                };

                await _recognizer.StartContinuousRecognitionAsync();
                _logger.LogInformation("Continuous speech recognition started");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting continuous speech recognition");
                OnError?.Invoke(this, ex);
                throw;
            }
        }

        public async Task<string> TranscribeAudioFileAsync(string audioFilePath)
        {
            try
            {
                _speechConfig = InitializeSpeechConfig();

                _logger.LogInformation("Transcribing audio file: {FilePath}", audioFilePath);

                using var audioConfig = AudioConfig.FromWavFileInput(audioFilePath);
                using var recognizer = new SpeechRecognizer(_speechConfig, audioConfig);
                var result = await recognizer.RecognizeOnceAsync();

                if (result.Reason == ResultReason.RecognizedSpeech)
                {
                    _logger.LogInformation("Transcription successful: {Text}", result.Text);
                    return result.Text;
                }
                else
                {
                    var message = $"Recognition failed: {result.Reason}";
                    _logger.LogWarning(message);
                    return message;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transcribing audio file: {FilePath}", audioFilePath);
                OnError?.Invoke(this, ex);
                return $"Error: {ex.Message}";
            }
        }

        public async Task StopContinuousRecognitionAsync()
        {
            try
            {
                _logger.LogInformation("Stopping continuous speech recognition");

                if (_recognizer != null)
                {
                    await _recognizer.StopContinuousRecognitionAsync();
                    _recognizer.Dispose();
                    _recognizer = null;
                    _logger.LogInformation("Continuous speech recognition stopped");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping continuous speech recognition");
                OnError?.Invoke(this, ex);
                throw;
            }
        }

        //public async Task UploadAudioToBackendAsync(string filePath)
        //{
        //    var backendUrl = _configuration["EncounterNotesURLUpload"]; // e.g., "https://yourapi/api/audio/upload"
        //    if (string.IsNullOrEmpty(backendUrl) || !File.Exists(filePath))
        //        throw new InvalidOperationException("Invalid upload URL or file path.");

        //    var accessToken = await SecureStorage.GetAsync("auth_token");
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new InvalidOperationException("Access token is missing.");

        //    using var httpClient = new HttpClient();
        //    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        //    using var form = new MultipartFormDataContent();
        //    using var fileStream = File.OpenRead(filePath);
        //    var fileContent = new StreamContent(fileStream);
        //    fileContent.Headers.ContentType = new MediaTypeHeaderValue("audio/webm");
        //    form.Add(fileContent, "audioFile", Path.GetFileName(filePath));


        //    var response = await httpClient.PostAsync(backendUrl, form);
        //    response.EnsureSuccessStatusCode();
        //}

        //public async Task PostTranscriptionsAsync(Guid id, Guid patientId, string visitType, Guid? orgId, bool subscription)
        //{
        //    if (_collectedSpeeches.Count > 0)
        //    {
        //        try
        //        {
        //            var speechApiUrl = _configuration["EncounterNotesURLSpeech"];
        //            if (string.IsNullOrWhiteSpace(speechApiUrl))
        //            {
        //                _logger.LogError("EncounterNotesURLSpeech is missing in configuration.");
        //                return;
        //            }
        //            var uri = new Uri(speechApiUrl);
        //            var accessToken = await SecureStorage.GetAsync("auth_token");

        //            var speeches = new SpeechRequest
        //            {
        //                Id = id,
        //                PCPId = _user != null && !string.IsNullOrEmpty(_user.id) ? Guid.Parse(_user.id) : Guid.Empty,
        //                PatientId = patientId,
        //                OrganizationId = new Guid("1B9C038A-3AAA-4783-A1F7-BB574E1F1BCB"),
        //                Speeches = _collectedSpeeches,
        //                VisitType = visitType,
        //                accessToken = accessToken
        //            };

        //            var bodyContent = System.Text.Json.JsonSerializer.Serialize(speeches);
        //            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
        //            var requestMessage = new HttpRequestMessage(HttpMethod.Post, uri)
        //            {
        //                Content = content
        //            };
        //            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        //            var response = await _httpClient.SendAsync(requestMessage);
        //            if (response.IsSuccessStatusCode)
        //            {
        //                var responseBody = await response.Content.ReadAsStringAsync();
        //                dynamic? result = JsonConvert.DeserializeObject(responseBody);
        //                if (result == null)
        //                {
        //                    _logger.LogError("Deserialization returned null for response body: {ResponseBody}", responseBody);
        //                    throw new InvalidOperationException("Failed to deserialize response body.");
        //                }
        //                if (result != null && result.firstRecordId != null)
        //                    _currentRecordingId = result.firstRecordId;
        //            }
        //            else
        //            {
        //                _logger.LogError("Failed to post transcriptions: {StatusCode}", response.StatusCode);
        //            }
        //            _collectedSpeeches.Clear();
        //        }
        //        catch (Exception ex)
        //        {
        //            _logger.LogError(ex, "Error posting transcriptions");
        //        }
        //    }
        //}

        public void Dispose()
        {
            if (!_disposed)
            {
                _pushStream?.Dispose();
                _conversationTranscriber?.Dispose();
                _disposed = true;
            }
        }
    }
}

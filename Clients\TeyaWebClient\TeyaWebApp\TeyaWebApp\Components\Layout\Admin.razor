﻿@inherits LayoutComponentBase
@using Microsoft.Extensions.Localization
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject NavigationManager Navigation
@inject ILogger<Admin> Logger
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage

@if (!_isThemeLoaded)
{
    <div class="loading-container">
        <MudProgressCircular Indeterminate="true" Style="width: 80px; height: 80px;" />
    </div>
}
else
{
    <MudPopoverProvider />
    <MudDialogProvider />
    <MudSnackbarProvider />

    <MudLayout>
        <CascadingValue Value="this">
            <MudAppBar Elevation="1">
                <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="DrawerToggle" />
                <MudSpacer />

                <div class="search-container">
                    <MudAutocomplete T="Member"
                                     SearchFunc="SearchMembers"
                                     ToStringFunc="@(member => member?.UserName)"
                                     Clearable="true"
                                     Placeholder="@Localizer["SearchUsername"]"
                                     Class="mx-2"
                                     @bind-Value="_selectedMember"
                                     Style="background-color: white; border-radius: 4px; width: 94%;"
                                     OnKeyDown="HandleKeyDown" />

                    <MudIconButton Icon="@Icons.Material.Filled.Search" Color="Color.Inherit" Edge="Edge.Start" OnClick="SearchAndNavigateAsync" />
                </div>
                <MudSpacer />
                <MudThemeProvider Theme="CurrentTheme" />

                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.AccountCircle" Size="Size.Large" Edge="Edge.End"
                               Style="color: white;" OnClick="OpenProfileDialog" />
            </MudAppBar>

            <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2"
                       Variant="@DrawerVariant.Mini" OpenMiniOnHover="true"
                       Style="@(_drawerOpen ? "width: 240px;" : "width: 50px;")">
                <MudDrawerHeader>
                    @if (_drawerOpen)
                    {
                        <MudText Typo="Typo.h5" Class="mt-1" Style="font-weight: bold;">@Localizer["TeyaHealth"]</MudText>
                    }
                </MudDrawerHeader>
                <AdminNav />
            </MudDrawer>

            <MudMainContent>
                @Body
            </MudMainContent>
        </CascadingValue>
    </MudLayout>
}

<style>
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }

    .search-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40%;
        margin: 0 auto;
    }
</style>

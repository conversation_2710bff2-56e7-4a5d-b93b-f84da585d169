﻿@using TeyaWebApp.TeyaAIScribeResources
@using System.Collections.Generic
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomAlertDialogAsync">@Localizer["Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomImmunizationAlertDialogAsync">@Localizer["Immunization Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomProcedureAlertDialogAsync">@Localizer["Procedure Alerts"]</MudButton>
<MudContainer Class="mt-4">
    <MudPaper Elevation="3" Class="pa-4">
        <MudText Typo="Typo.h4" Class="mb-4">@Localizer["ClinicalDecisionSupportSystem"]</MudText>

        <MudGrid Class="mb-4">
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="searchString"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search"
                              Placeholder="@Localizer["SearchAlerts"]"
                              Immediate="true"
                              Class="mt-0" />
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex justify-end">
                <MudSelect T="string"
                           Label="@Localizer["FilterBySeverity"]"
                           @bind-Value="selectedSeverity"
                           Class="ml-auto"
                           Style="min-width: 200px">
                    <MudSelectItem Value="@("All")">@Localizer["AllSeverities"]</MudSelectItem>
                    <MudSelectItem Value="@("High")">@Localizer["High"]</MudSelectItem>
                    <MudSelectItem Value="@("Medium")">@Localizer["Medium"]</MudSelectItem>
                    <MudSelectItem Value="@("Low")">@Localizer["Low"]</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>

        <MudCard>
            @if (!FilteredAlerts.Any())
            {
                <MudAlert Severity="Severity.Info" Class="my-4 mx-4">
                    @Localizer["NoAlertsFound"]
                </MudAlert>
            }
            else
            {
                <MudDataGrid Items="@FilteredAlerts"
                             Filterable="false"
                             SortMode="SortMode.Multiple"
                             Hover="true"
                             Dense="true">
                    <Columns>
                        <PropertyColumn Property="x => x.CreatedDate" Title="@Localizer["Date"]" Sortable="true" Format="MM/dd/yyyy" />
                        <PropertyColumn Property="x => x.PatientName" Title="@Localizer["Patient"]" Sortable="true" />
                        <PropertyColumn Property="x => x.AlertType" Title="@Localizer["AlertType"]" Sortable="true" />
                        <PropertyColumn Property="x => x.Description" Title="@Localizer["Reason"]" />
                        <TemplateColumn Title="@Localizer["Severity"]" Sortable="true" SortBy="x => x.Severity">
                            <CellTemplate>
                                @{
                                    var alert = context.Item;
                                    var color = alert.Severity switch
                                    {
                                        "High" => Color.Error,
                                        "Medium" => Color.Warning,
                                        "Low" => Color.Info,
                                        _ => Color.Default
                                    };
                                }
                                <MudChip Color="@color" Size="Size.Small" Class="mr-2">@alert.Severity</MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="@Localizer["Actions"]">
                            <CellTemplate>
                                @{
                                    var alert = context.Item;
                                }
                                <MudTooltip Text="@Localizer["ViewDetails"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   OnClick="@(() => OpenDetails(alert))" />
                                </MudTooltip>
                                <MudTooltip Text="@Localizer["Dismiss"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.CheckCircle"
                                                   Color="Color.Success"
                                                   Size="Size.Small"
                                                   OnClick="@(() => DismissAlert(alert))" />
                                </MudTooltip>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <PagerContent>
                        <MudDataGridPager />
                    </PagerContent>
                </MudDataGrid>
            }
        </MudCard>
    </MudPaper>
</MudContainer>
@if (selectedAlert != null)
{
    <MudDialog @bind-IsVisible="dialogVisible" Options="dialogOptions">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Class="mr-2" />
                @Localizer["AlertDetails"]
            </MudText>
        </TitleContent>
        <DialogContent>
            <MudPaper Class="pa-4" Elevation="0">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["PatientLabel"]</strong> @selectedAlert.PatientName</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["AlertTypeLabel"]</strong> @selectedAlert.AlertType</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["SeverityLabel"]</strong> @selectedAlert.Severity</MudText>
                        <MudDivider Class="my-4" />
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["ProblemDescriptionLabel"]</strong></MudText>
                        <MudText Class="mb-4">@selectedAlert.Description</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["RecommendedActionLabel"]</strong></MudText>
                        <MudText>@selectedAlert.Solution</MudText>

                        @if (selectedAlert.AlertType == Localizer["DrugInteraction"])
                        {
                            <MudDivider Class="my-4" />
                            <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["InteractingMedicationsLabel"]</strong></MudText>
                            <MudList T="string" Dense="true">
                                @foreach (var med in selectedAlert.AdditionalInfo.Split(","))
                                {
                                    <MudListItem Icon="@Icons.Material.Filled.Medication" Text="@med.Trim()" />
                                }
                            </MudList>
                        }
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(() => dialogVisible = false)" Color="Color.Default">@Localizer["Close"]</MudButton>
            <MudButton OnClick="@(() => DismissAlert(selectedAlert))" Color="Color.Primary">@Localizer["MarkAsReviewed"]</MudButton>
        </DialogActions>
    </MudDialog>
}


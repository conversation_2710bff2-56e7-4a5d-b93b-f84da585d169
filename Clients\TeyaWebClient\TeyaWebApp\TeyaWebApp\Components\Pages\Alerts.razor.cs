﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class Alerts
    {
        [Inject] private ILogger<Alerts> Logger { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IAlertService AlertService { get; set; }
        [Inject] private PatientService PatientService { get; set; }

        private string searchString = "";
        private string selectedSeverity = "All";
        private bool dialogVisible = false;
        private Alert selectedAlert = null;
        private List<Alert> alerts = new List<Alert>();
        private Guid patientId;
        private Guid? organizationId;

        private DialogOptions dialogOptions = new DialogOptions
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseOnEscapeKey = true
        };

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Get patient data
                patientId = PatientService.PatientData.Id;
                organizationId = PatientService.PatientData.OrganizationID;

                // Load alerts from service
                await LoadAlerts();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing Alerts component");
                Snackbar.Add("Error loading alerts. Please try again later.", Severity.Error);
                alerts = new List<Alert>();
            }
        }

        private async Task LoadAlerts()
        {
            try
            {
                var loadedAlerts = await AlertService.GetAllByIdAndIsActiveAsync(patientId, organizationId, false);
                if (loadedAlerts != null)
                {
                    alerts = loadedAlerts;
                }
                else
                {
                    alerts = new List<Alert>();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading alerts");
                Snackbar.Add("Error loading alerts from the server. Please try again later.", Severity.Error);
                alerts = new List<Alert>();
            }
        }

        public void OpenCustomAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomLabAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        public void OpenCustomImmunizationAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomImmunizationAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        /// <summary>
        /// Custom Alert Dailog Box For Storing Custom Procedure Alerts
        /// </summary>
        public void OpenCustomProcedureAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomProcedureAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        private IEnumerable<Alert> FilteredAlerts => alerts
            .Where(x => (string.IsNullOrWhiteSpace(searchString) ||
                         x.PatientName.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                         x.AlertType.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                         x.Description.Contains(searchString, StringComparison.OrdinalIgnoreCase)))
            .Where(x => selectedSeverity == "All" || x.Severity == selectedSeverity)
            .ToList();

        private void OpenDetails(Alert alert)
        {
            selectedAlert = alert;
            dialogVisible = true;
        }

        private async Task DismissAlert(Alert alert)
        {
            try
            {
                alert.IsActive = false;
                await AlertService.UpdateAlertAsync(alert, organizationId, false);
                alerts.Remove(alert);

                Snackbar.Add($"Alert for {alert.PatientName} has been dismissed", Severity.Success);
                dialogVisible = false;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDismissingAlert"]);
            }
        }
    }
}
﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
using System.Linq;
using TeyaWebApp.TeyaAIScribeResource;
using System.Text.RegularExpressions;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Allergies : ComponentBase
    {
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public IAllergyService AllergyService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private string drugName;
        private string editorContent;
        private MudDialog _allergicdialog;
        private SfRichTextEditor RichTextEditor;
        public SfGrid<Allergy> AllergyGrid { get; set; }
        private List<Allergy> _allergiesData { get; set; }
        private List<Allergy> _localAllergiesData { get; set; }
        private List<Allergy> _originalAllergiesData { get; set; }
        private List<Allergy> _deleteList { get; set; } = new List<Allergy>();
        protected Dictionary<string, string> DrugNames { get; set; } = new Dictionary<string, string>();
        protected Dictionary<string, string> FDBAllergiesList { get; set; } = new Dictionary<string, string>();

        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        private Guid? OrganizationID { get; set; }
        public enum Source { RxNorm,FDB }
        private string selectedDatabase = Source.RxNorm.ToString();

        [Inject] IDialogService DialogService { get; set; }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };

        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            OrganizationID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            _originalAllergiesData = await AllergyService.GetAllergyByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription);
            _localAllergiesData = _originalAllergiesData.ToList();
            editorContent = GenerateRteContent(_localAllergiesData);
            var FDBAllergies = await FDBService.GetAllergies();
            var drugNamesList = await RxNormService.GetAllDrugNames();
            DrugNames = drugNamesList.ToDictionary(name => RxNormService.GetRxcuiByName(name), name => name);
            FDBAllergiesList = FDBAllergies.ToDictionary(name => $"{name.DAM_CONCEPT_ID} {name.DAM_CONCEPT_ID_TYP}", name => name.DAM_CONCEPT_ID_DESC);
        }

        /// <summary>
        /// Generates rich text editor content from allergy data.
        /// </summary>
        private string GenerateRteContent(List<Allergy> allergies)
        {
            return string.Join("<p>", allergies.Select(m => $"{m.DrugName}, {m.Classification}, {m.Agent} {m.Reaction}, {m.Type}"));
        }

        /// <summary>
        /// Update value in Drug Name List
        /// </summary>
        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            drugName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>
        protected Task<IEnumerable<string>> SearchDrugNames(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IEnumerable<string> result = Enumerable.Empty<string>();

            if (selectedDatabase == Source.RxNorm.ToString())
            {
                result = string.IsNullOrWhiteSpace(value)
                    ? DrugNames.Values.AsEnumerable()
                    : DrugNames.Values
                        .Where(b => !string.IsNullOrEmpty(b) && b.Contains(value, StringComparison.OrdinalIgnoreCase))
                        .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                result = string.IsNullOrWhiteSpace(value)
                    ? FDBAllergiesList.Values.AsEnumerable()
                    : FDBAllergiesList.Values
                        .Where(b => !string.IsNullOrEmpty(b) && b.Contains(value, StringComparison.OrdinalIgnoreCase))
                        .ToList();
            }

            return Task.FromResult(result);
        }


        /// <summary>
        /// Add function to add new row.
        /// </summary>
        private async void AddNewAllergy()
        {
            var newAllergy = new Allergy
            {
                MedicineId = Guid.NewGuid(),
                PatientId = PatientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrganizationID,
                DrugName = string.IsNullOrEmpty(drugName) ? "Not Specified" : drugName,
                Classification = (drugName == "Not Specified" || string.IsNullOrEmpty(drugName)) ? "Unstructured" : "Structured",
                Agent = "Not Specified",
                Type = "Not Specified",
                Reaction = "Not Specified",
                IsActive = true,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientID,
                CreatedOn = DateTime.Now,
                UpdatedOn = DateTime.Now,
                AllergyInfo = "Not Specified",
                Substance = "Not Specified",
                DrugDetails = null,
                IsNew = true
            };

            _localAllergiesData.Add(newAllergy);
            AllergyGrid.Refresh();
            ResetInputFields();
            StateHasChanged();
        }

        private void ResetInputFields()
        {
            drugName = string.Empty;
        }

        /// <summary>
        /// Handles row deletions in the grid.
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<Allergy> args)
        {
            
        }

        /// <summary>
        /// Handles row updates in the grid.
        /// </summary>
        public  async Task ActionBeginHandler(ActionEventArgs<Allergy> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedOn = DateTime.Now;
                _deleteList.Add(args.Data);
                _localAllergiesData.Remove(args.Data);
            }
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!Regex.IsMatch(args.Data.Classification, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["ClassificationInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Agent, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["AgentInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Reaction, "^[A-Za-z]+$"))
                {
                    Snackbar.Add(Localizer["ReactionInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                if (!Regex.IsMatch(args.Data.Type, "^[A-Za-z0-9\\-]+$"))
                {
                    Snackbar.Add(Localizer["TypeInvalid"], Severity.Error);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedOn = DateTime.Now;
            }
        }

        /// <summary>
        /// Save the changes.
        /// </summary>
        private async Task SaveChanges()
        {
            try
            {
                var activeLocalAllergies = _localAllergiesData.Where(a => !_deleteList.Contains(a)).ToList();
                var newAllergies = activeLocalAllergies.Where(a => a.IsNew).ToList();
                var updatedAllergies = activeLocalAllergies.Where(a => !a.IsNew).ToList();

                await AllergyService.UpdateAllergyListAsync(newAllergies, updatedAllergies, _deleteList, PatientID, OrganizationID, Subscription);

                _originalAllergiesData = await AllergyService.GetAllergyByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription);

                editorContent = GenerateRteContent(_originalAllergiesData);

                foreach (var allergy in _localAllergiesData)
                {
                    allergy.IsNew = false;
                }

                _deleteList.Clear();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                CloseAddTaskDialog();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving changes: {ex}");
                Snackbar.Add("SaveError", Severity.Error);
            }
        }

        /// <summary>
        /// Undo Changes When click on cancel.
        /// </summary>
        private async Task CancelChanges()
        {
            _localAllergiesData = _originalAllergiesData.ToList();
            _deleteList.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Opens the dialog and initializes local data.
        /// </summary>
        private async Task OpenAddTaskDialog()
        {
            _localAllergiesData = _originalAllergiesData.ToList();
            _deleteList.Clear();

            _allergicdialog.ShowAsync();
        }

        /// <summary>
        /// Closes the dialog.
        /// </summary>
        private void CloseAddTaskDialog()
        {
            ResetInputFields();
            _allergicdialog.CloseAsync();
        }
    }
}
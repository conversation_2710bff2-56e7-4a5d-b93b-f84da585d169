using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Assessments
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IAssessmentsService _AssessmentsService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        private List<ChiefComplaintDTO> chiefComplaints = new();
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private List<string> chiefComplaintDescriptions = new List<string>();

        public string ICDName { get; set; }
        public enum Source { FDB, CMS }
       
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __Assessments;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrganizationID { get; set; }
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();

        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();
        private string selectedDatabase = Source.CMS.ToString();

        public SfGrid<TeyaUIModels.Model.AssessmentsData> AssessmentsGrid { get; set; }

        private List<TeyaUIModels.Model.AssessmentsData> AddList = new();
        private List<TeyaUIModels.Model.AssessmentsData> _Assessments { get; set; }
        private List<TeyaUIModels.Model.AssessmentsData> deleteAssessmentslist { get; set; } = new List<TeyaUIModels.Model.AssessmentsData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = _PatientService.PatientData.Id;
                OrganizationID = _PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrganizationID, Subscription))
                    .GroupBy(c => c.Description)
                    .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                    .ToList();
                SharedNotesService.OnChange += UpdateComplaints;  
                chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();

                _icdCodes = await _ICDService.GetAllICDCodesAsync();
                fdb_ICD = await FDBService.GetICD();
                _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrganizationID, Subscription);
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving ICD codes: {ex.Message}");
            }
        }
        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();  
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            ICDName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        public void Dispose()
        {
            SharedNotesService.OnChange -= UpdateComplaints;  
        }
        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not AssessmentsData assessment) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", assessment.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    assessment.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        assessment.CheifComplaintId = selectedComplaint.Id;
                        Console.WriteLine(assessment.CheifComplaintId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _Assessments
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])}  <br><strong>{Localizer["Diagnosis"]}:</strong> {s.Diagnosis} <br><strong>{Localizer["Specify"]}:</strong> {s.Specify}</p>"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>

        private async Task OpenNewDialogBox()
        {
            await __Assessments.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __Assessments.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IEnumerable<string> searchResults = Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                searchResults = _icdCodes
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (
                            (!string.IsNullOrEmpty(icd.Code) && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.Description) && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                searchResults = fdb_ICD
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") &&
                        (
                            (!string.IsNullOrEmpty(icd.ICD_CD) && icd.ICD_CD.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.ICD_DESC) && icd.ICD_DESC.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }

            return Task.FromResult(searchResults);
        }


        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewDiagnosis()
        {
            var newDiagnosis = new TeyaUIModels.Model.AssessmentsData
            {
                AssessmentsID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Diagnosis = ICDName,
                IsActive = true,
            };

            AddList.Add(newDiagnosis);
            _Assessments.Add(newDiagnosis);
            await AssessmentsGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            ICDName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.AssessmentsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleleAssessments = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.AssessmentsID == deleleAssessments.AssessmentsID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    deleleAssessments.IsActive = false;
                    deleleAssessments.UpdatedDate = DateTime.Now;
                    deleteAssessmentslist.Add(deleleAssessments);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.AssessmentsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }
        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Any(assessment => string.IsNullOrWhiteSpace(assessment.CheifComplaint)))
            {
                Snackbar.Add("Each assessment must have a Chief Complaint.", Severity.Warning);
                return;
            }
            if (AddList.Count != 0)
            {
                await _AssessmentsService.AddAssessmentsAsync(AddList, OrganizationID, Subscription);
                SharedNotesService.AssessmentsChanged();
            }
            await _AssessmentsService.UpdateAssessmentsListAsync(_Assessments, OrganizationID, Subscription);
            await _AssessmentsService.UpdateAssessmentsListAsync(deleteAssessmentslist, OrganizationID, Subscription);
            deleteAssessmentslist.Clear();
            AddList.Clear();
            UpdateEditorContent();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deleteAssessmentslist.Clear();
            AddList.Clear();
            _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrganizationID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }
    }
}
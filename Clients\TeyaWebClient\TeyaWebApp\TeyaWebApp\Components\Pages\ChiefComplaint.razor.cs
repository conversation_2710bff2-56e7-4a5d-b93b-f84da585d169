﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Blazored.SessionStorage;
using Microsoft.Extensions.Logging;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
namespace TeyaWebApp.Components.Pages
{
    public partial class ChiefComplaint
    {
        [Inject] private ISessionStorageService SessionStorage { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }

        private List<ChiefComplaintDTO> complaints = new();

        [Inject]
        private ILogger<ChiefComplaintDTO> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }

        public string Name { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        public List<string> DistinctComplaintSuggestions { get; private set; } = new();

        private List<ChiefComplaintDTO> addedComplaints = new();
        private List<ChiefComplaintDTO> updatedComplaints = new();
        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<ChiefComplaintDTO> ComplaintsGrid;
        private string richTextContent = string.Empty;
        private string complaintDescription = string.Empty;
        private List<ChiefComplaintDTO> deleteList = new();


        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientService.PatientData.Id;
                OrgID = PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                LocalData = await ChiefComplaintService.GetProcessedComplaintsAsync(OrgID, Subscription);
                await LoadComplaintsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }
        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };

        private async Task AddComplaint()
        {
            if (string.IsNullOrWhiteSpace(complaintDescription))
            {
                Logger.LogWarning(Localizer["Please enter a complaint description."]);
                return;
            }

            // Check duplicates before adding
            var isDuplicate = complaints.Concat(addedComplaints)
                .Any(c => c.Description.Equals(complaintDescription, StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                return;
            }

            var newComplaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                Description = complaintDescription,
                DateOfComplaint = DateTime.Now,
                OrganizationId = (Guid)PatientService.PatientData.OrganizationID,
                PcpId = Guid.Parse(User.id),
                IsDeleted = false
            };

            try
            {
                complaints.Add(newComplaint);
                addedComplaints.Add(newComplaint);
                await ComplaintsGrid.Refresh();

                // Update LocalData only if not existing there
                if (!LocalData.Any(c => c.Description.Equals(complaintDescription, StringComparison.OrdinalIgnoreCase)))
                {
                    LocalData.Add(newComplaint);
                }

                complaintDescription = string.Empty;

                Snackbar.Add(Localizer["Complaint added successfully."], Severity.Success);

                Logger.LogInformation(Localizer["Complaint added successfully."]);

            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error adding complaint"]);
            }
        }

        private string GenerateRichTextContent() => string.Join(" ",
            complaints.OrderByDescending(c => c.DateOfComplaint)
              .Select(c => $"<ul><li style='margin-left: 20px;'><b>{c.DateOfComplaint:yyyy-MM-dd}</b> : {c.Description}</li></ul>"));


        private async Task ActionBeginHandler(ActionEventArgs<ChiefComplaintDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Delete"],
                    Localizer["Do you want to delete this entry?"],
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return; 
                }

                // Only mark for deletion if user confirms
                args.Data.IsDeleted = true;
                deleteList.Add(args.Data);
                Snackbar.Add(Localizer["Entry deleted successfully."], Severity.Warning);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Check duplicates in both existing data and pending additions
                bool isDuplicate = complaints.Concat(addedComplaints)
                    .Where(c => !deleteList.Contains(c)) // Exclude deleted items
                    .Any(c =>
                        c.Description?.Trim().Equals(args.Data.Description?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        c.Id != args.Data.Id);

                if (isDuplicate)
                {
                    Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                    args.Cancel = true;
                    await ComplaintsGrid.CloseEditAsync();
                    CloseBrowsePopup();
                    return;
                }

                if (!addedComplaints.Contains(args.Data) && !updatedComplaints.Contains(args.Data))
                {
                    updatedComplaints.Add(args.Data);
                }
            }
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task SaveChanges()
        {
            try
            {
                if (addedComplaints.Any())
                {
                    foreach (var newEntry in addedComplaints)
                    {
                        await ChiefComplaintService.AddAsync(newEntry, OrgID, Subscription);
                    }
                    List<ChiefComplaintDTO> CheifComplaintsById = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription)).ToList();
                    var filteredComplaints = CheifComplaintsById
                        .GroupBy(c => c.Description)
                        .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First()) 
                        .ToList();
                    SharedNotesService.AddChiefComplaints(addedComplaints);
                    addedComplaints.Clear();
                }
                if (updatedComplaints.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(updatedComplaints, OrgID, Subscription);
                    updatedComplaints.Clear();
                }

                if (deleteList.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(deleteList, OrgID, Subscription);
                    deleteList.Clear();
                }

                await LoadComplaintsAsync();
                richTextContent = GenerateRichTextContent();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                Logger.LogInformation(Localizer["Changes saved successfully."]);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error saving changes"]);
            }
        }

        private async Task CancelChanges()
        {
            deleteList.Clear();
            addedComplaints.Clear();
            updatedComplaints.Clear();
            await LoadComplaintsAsync();
            CloseBrowsePopup();
            Logger.LogInformation(Localizer["Changes canceled."]);
        }

        private async Task LoadComplaintsAsync()
        {
            try
            {
                complaints = await ChiefComplaintService.LoadComplaintsAsync(PatientId, OrgID, Subscription);

                richTextContent = GenerateRichTextContent();

                if (ComplaintsGrid != null)
                    await ComplaintsGrid.Refresh();

                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error loading complaints"]);
            }
        }

        private void CloseBrowsePopup()
        {
            complaintDescription = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopupAsync()
        {
            showBrowsePopup.ShowAsync();
            if (ComplaintsGrid != null)
                await ComplaintsGrid.Refresh();

        }
    }
}

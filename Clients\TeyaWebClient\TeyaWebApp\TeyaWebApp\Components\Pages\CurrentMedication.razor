﻿@page "/CurrentMedication"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.DropDowns

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" @onclick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_currentmedicdialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Current Medication"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <!-- Form Inputs Section -->
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <div style="padding: 20px 20px 0 20px; background-color: #ffffff; display: flex; align-items: center; gap: 20px;">
                    <div style="flex-grow: 1; max-width: 300px;">
                        <MudSelect T="string" Label="@Localizer["Select Database"]" @bind-Value="selectedDatabase"
                                   Dense="true" Margin="Margin.Dense" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("RxNorm")">@Localizer["RxNorm"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@("FDB")">@Localizer["FDB"]</MudSelectItem>
                        </MudSelect>
                    </div>
                </div>

                <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                    @if (selectedDatabase == Source.RxNorm.ToString())
                    {
                        <!-- RxNorm UI -->
                        <MudGrid Spacing="3" Style="align-items: center;">
                            <MudItem xs="4">
                                <MudAutocomplete T="string" Label="@Localizer["Search Brand Names"]" Value="@drugName"
                                                 ValueChanged="OnDrugNameChanged" SearchFunc="SearchBrandNames"
                                                 ToStringFunc="@(s => s)" CoerceText="true" Clearable="true"
                                                 Dense="true" ResetValueOnEmptyText="true" Margin="Margin.Dense"
                                                 Variant="Variant.Outlined" MinCharacters="3" Style="width: 100%;" />
                            </MudItem>
                            <MudItem xs="4">
                                <MudSelect T="string" Label="@Localizer["Dosage & InTake"]" @bind-Value="finalSBD"
                                           Dense="true" Margin="Margin.Dense" Variant="Variant.Outlined" Style="width: 100%;">
                                    @foreach (var drugs in BrandSBD)
                                    {
                                        <MudSelectItem T="string" Value="@drugs">@drugs</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="4">
                                <MudButton Color="Color.Primary" OnClick="AddNewMedication" Variant="Variant.Filled"
                                           Dense="true" Style="min-width: 100px; height: 40px;">
                                    @Localizer["Add"]
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    }

                    else if (selectedDatabase == Source.FDB.ToString())
                    {
                        <MudItem xs="12">
                            <MudPaper Elevation="0" Class="pa-2 mb-2">
                                <MudGrid>
                                    <MudItem xs="6" md="3">
                                        <!-- Medication Autocomplete -->
                                        <MudAutocomplete T="FDBMedicationName"
                                                         Label="@Localizer["Select Medication"]"
                                                         SearchFunc="SearchMedications"
                                                         ToStringFunc="@(med => med?.MED_NAME)"
                                                         Clearable="true"
                                                         MinCharacters="3"
                                                         ResetValueOnEmptyText="true"
                                                         ValueChanged="OnMedicationSelected"
                                                         Value="selectedMedication"
                                                         Dense="true"
                                                         Variant="Variant.Outlined" />
                                    </MudItem>

                                    <MudItem xs="6" md="3">
                                        <MudSelect T="FDBRoutedMedication"
                                                   Label="@Localizer["Select Route"]"
                                                   Value="selectedRoutedMedication"
                                                   ValueChanged="OnRoutedMedicationSelected"
                                                   ToStringFunc="@(r => GetRouteName(r?.MED_ROUTE_ID))"
                                                   Dense="true"
                                                   Clearable="true"
                                                   Variant="Variant.Outlined">
                                            @if (RoutedMedications?.Any() == true)
                                            {
                                                @foreach (var item in RoutedMedications)
                                                {
                                                    <MudSelectItem Value="@item">@GetRouteName(item.MED_ROUTE_ID)</MudSelectItem>
                                                }
                                            }
                                        </MudSelect>
                                    </MudItem>

                                    <MudItem xs="6" md="3">
                                        <!-- Routed Dosage Form Medication Select -->
                                        <MudSelect T="FDBRoutedDosageFormMedication"
                                                   Label="@Localizer["Select Take"]"
                                                   Value="selectedRoutedDosageFormMedication"
                                                   ValueChanged="OnRoutedDosageFormMedicationSelected"
                                                   ToStringFunc="@(r => r?.MED_ROUTED_DF_MED_ID_DESC)"
                                                   Dense="true"
                                                   Clearable="true"
                                                   Variant="Variant.Outlined">
                                            @if (RoutedDosageFormMedications?.Any() == true)
                                            {
                                                @foreach (var item in RoutedDosageFormMedications)
                                                {
                                                    <MudSelectItem Value="@item">@GetTakeName(item.MED_DOSAGE_FORM_ID)</MudSelectItem>
                                                }
                                            }
                                        </MudSelect>
                                    </MudItem>

                                    <MudItem xs="6" md="3">
                                        <!-- Final Medication Select -->
                                        <MudSelect T="FDBMedication"
                                                   Label="@Localizer["Select Strength/Dosage"]"
                                                   Value="selectedFinalMedication"
                                                   ValueChanged="OnFinalMedicationSelected"
                                                   ToStringFunc="@(r => r?.MED_MEDID_DESC)"
                                                   Dense="true"
                                                   Clearable="true"
                                                   Variant="Variant.Outlined">
                                            @if (FinalMedications?.Any() == true)
                                            {
                                                @foreach (var item in FinalMedications)
                                                {
                                                    @if (@item.MED_STRENGTH != null || @item.MED_STRENGTH_UOM != null)
                                                    {
                                                        <MudSelectItem Value="@item">@item.MED_STRENGTH @item.MED_STRENGTH_UOM</MudSelectItem>
                                                    }
                                                    else
                                                    {
                                                        <MudSelectItem Value="@item">@item.MED_MEDID_DESC</MudSelectItem>
                                                    }
                                                }
                                            }
                                        </MudSelect>
                                    </MudItem>
                                </MudGrid>

                                <!-- Improved alignment for text and button -->
                                <div class="d-flex align-center mt-3">
                                    <MudText Class="flex-grow-1 mr-4" Style="font-size: 14px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                        Drug Details : @(selectedFinalMedication?.MED_MEDID_DESC ?? "")
                                    </MudText>
                                    <MudButton Color="Color.Primary"
                                               OnClick="AddNewMedication"
                                               Variant="Variant.Filled"
                                               Dense="true"
                                               Style="min-width: 100px; height: 36px;">
                                        @Localizer["Add"]
                                    </MudButton>
                                </div>
                            </MudPaper>
                        </MudItem>

                    }
                </div>


                <!-- Medication Table -->
                <SfGrid @ref="MedicinesGrid"
                        TValue="ActiveMedication"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@medications"
                        AllowPaging="true"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="ActiveMedication"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="BrandName" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                        <GridColumn Field="DrugDetails" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Route" HeaderText="@Localizer["Route"]" TextAlign="TextAlign.Center" Width="50" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Take" HeaderText="@Localizer["Take"]" TextAlign="TextAlign.Center" Width="50" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Strength" HeaderText="@Localizer["Strength"]" TextAlign="TextAlign.Center" Width="50" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1"></GridColumn>
                        <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                        <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>

                        <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)"
                                    HeaderText="@Localizer["Chief Complaint"]"
                                    TextAlign="TextAlign.Center"
                                    Width="200"
                                    EditTemplate="@ChiefComplaintEditTemplate">
                        </GridColumn>


                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>
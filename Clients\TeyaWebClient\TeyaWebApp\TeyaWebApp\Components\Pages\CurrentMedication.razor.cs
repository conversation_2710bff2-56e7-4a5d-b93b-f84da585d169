﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Blazored.SessionStorage;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Unity;
using static MudBlazor.Colors;

namespace TeyaWebApp.Components.Pages
{
    public partial class CurrentMedication : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        private List<ChiefComplaintDTO> chiefComplaints = new();
        [Inject] ISessionStorageService SessionStorage { get; set; }
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public  IFDBService FDBService { get; set; }
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        protected List<string> BrandNames { get; set; } = new List<string>();
        public List<string> BrandSBD { get; set; } = new List<string>();
        public string drugName { get; set; }
        public string finalSBD { get; set; }
        private MudDialog _currentmedicdialog;
        public string _value;
        private string _Quantity;
        private string _Frequency;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };

        public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
        [Inject]
        private IMemberService _MemberService { get; set; }
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        private Guid? OrgID { get; set; }
        private string editorContent;
        private List<ActiveMedication> deleteList { get; set; } = new List<ActiveMedication>();
        private List<ActiveMedication> AddList = new();
        private List<ActiveMedication> medications { get; set; }
        [Inject]
        private IChiefComplaintService ChiefComplaintService { get; set; }

        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private string complaintDescription = string.Empty;
        private List<string> chiefComplaintDescriptions = new List<string>();
        private List<FDBRouteLookUp> FDBRouteLookUps { get; set; }
        private List<FDBTakeLookUp> FDBTakeLookUps { get; set; }

        private Dictionary<string, string> routeNameLookup;
        private Dictionary<string, string> takeNameLookup;


        /// <summary>
        /// get list of all Brand Names from RxNorm and Patient Prescription Medication from database
        /// </summary>

        protected override async Task OnInitializedAsync()
        {

            PatientID = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientID, OrgID, Subscription))
                    .GroupBy(c => c.Description)
                    .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                    .ToList();
            SharedNotesService.OnChange += UpdateComplaints;  
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            editorContent = string.Join("<p>",
                medications.OrderByDescending(m => m.StartDate)
                .Select(m => $"Brand Name: {m.BrandName}, Composition: {m.DrugDetails}, Quantity: {m.Quantity}, " +
                             $"Start Date: {(m.StartDate.HasValue ? m.StartDate.Value.ToString("MM-dd-yyyy") : "")}, " +
                             $"End Date: {(m.EndDate.HasValue ? m.EndDate.Value.ToString("MM-dd-yyyy") : "")}"));

            FDBmedications = await FDBService.GetAllFDBMedications();
            RxMedication = await RxNormService.GetAllRxNormMedications();
            BrandNames = RxMedication.Select(m => m.STR).Distinct().ToList();
            FDBRouteLookUps = await FDBService.GetFDBRouteLookUp();
            FDBTakeLookUps = await FDBService.GetFDBTakeLookUp();
            routeNameLookup = FDBRouteLookUps.ToDictionary(x => x.MED_ROUTE_ID, x => x.Route_Name);
            takeNameLookup = FDBTakeLookUps.ToDictionary(x => x.MED_DOSAGE_FORM_ID, x => x.Take_Name);
        }


        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not ActiveMedication medication) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", medication.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    medication.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        medication.CheifComplaintId = selectedComplaint.Id;
                        Console.WriteLine(medication.CheifComplaintId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };

        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();  
        }

        public void Dispose()
        {
            SharedNotesService.OnChange -= UpdateComplaints;  
        }
        /// <summary>
        /// Update value in Drug Name List
        /// </summary>

        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;

            if (!string.IsNullOrEmpty(value))
            {
                var BrandSBDList = await RxNormService.GetRxNormSBDCMedications(value);
                BrandSBD = BrandSBDList.Select(m => m.STR).Distinct().ToList();
                finalSBD = null;
                StateHasChanged();

            }
            else
            {
                BrandSBD.Clear();
                finalSBD = null;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>

        protected Task<IEnumerable<string>> SearchBrandNames(string value, CancellationToken cancellationToken)
        {
            IEnumerable<string> result;

            if (cancellationToken.IsCancellationRequested)
            {
                result = Enumerable.Empty<string>();
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                result = BrandNames.AsEnumerable();
            }
            else
            {
                result = BrandNames.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
            }

            return Task.FromResult(result);
        }

        /// <summary>
        /// Add medication to database & Update to grid,RichTextEditor
        /// </summary>
        private async void AddNewMedication()
        {
            if(selectedDatabase == @Localizer["RxNorm"])
            {
                if (string.IsNullOrEmpty(drugName) || string.IsNullOrEmpty(finalSBD))
                {
                        Snackbar.Add(@Localizer["Please fill in both Brand Name and Drug Details fields"], Severity.Warning);
                        return;
                }
                var newMedication = new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = PatientID,
                    PCPId = Guid.Parse(User.id),
                    OrganizationId = _PatientService.PatientData.OrganizationID ?? Guid.Empty,
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = PatientID,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    BrandName = drugName,
                    DrugDetails = finalSBD,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    isActive = true,
                    StartDate = null,
                    EndDate = null,
                };

                AddList.Add(newMedication);

                medications.Add(newMedication);

                await MedicinesGrid.Refresh();
                ResetInputFields();
            }
            else if (selectedDatabase == @Localizer["FDB"])
            {
                if (selectedMedication == null ||
                    selectedRoutedMedication == null ||
                    selectedRoutedDosageFormMedication == null ||
                    selectedFinalMedication == null)
                {
                    Snackbar.Add(@Localizer["Please fill all fields"], Severity.Warning);
                    return;
                }
                string selectedstrength;
                if (selectedFinalMedication.MED_STRENGTH!=null && selectedFinalMedication.MED_STRENGTH_UOM != null)
                {
                    selectedstrength = selectedFinalMedication.MED_STRENGTH + " " + selectedFinalMedication.MED_STRENGTH_UOM;
                }
                else
                {
                    selectedstrength = selectedFinalMedication.MED_MEDID_DESC;
                }
                var newMedication = new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = PatientID,
                    PCPId = Guid.Parse(User.id),
                    OrganizationId = _PatientService.PatientData.OrganizationID ?? Guid.Empty,
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = PatientID,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    BrandName = selectedMedication.MED_NAME,
                    DrugDetails = selectedFinalMedication.MED_MEDID_DESC,
                    Route = GetRouteName(selectedRoutedMedication.MED_ROUTE_ID),
                    Take = GetTakeName(selectedRoutedDosageFormMedication.MED_DOSAGE_FORM_ID),
                    Strength = selectedstrength,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    isActive = true,
                    StartDate = null,
                    EndDate = null,
                };

                AddList.Add(newMedication);

                medications.Add(newMedication);

                await MedicinesGrid.Refresh();
                ResetInputFields();
            }
            
        }

        /// <summary>
        /// Reset Fields for closing
        /// </summary>
        private async void ResetInputFields()
        {
            drugName = string.Empty;
            finalSBD = null;
            BrandSBD.Clear();
            _Quantity = null;
            _Frequency = null;
            selectedMedication = null;
            selectedRoutedMedication = null;
            RoutedMedications.Clear();
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>

        public void ActionCompletedHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedMedication = args.Data as ActiveMedication;
                var existingItem = AddList.FirstOrDefault(v => v.MedicineId == deletedMedication.MedicineId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.isActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }


        public void ActionBeginHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue &&
                    args.Data.StartDate > args.Data.EndDate)
                {
                    Snackbar.Add(@Localizer["Start Date cannot be after End Date"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;

            }

        }
        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Changes to be made when clicking on save in dialog
        /// </summary>

        private async Task SaveChanges()
        {
            if (AddList.Any(medication => string.IsNullOrWhiteSpace(medication.CheifComplaint)))
            {
                Snackbar.Add("Each medication must have a Chief Complaint.", Severity.Warning);
                return;
            }
            if (AddList.Count != 0)
            {
                await CurrentMedicationService.AddMedicationAsync(AddList, OrgID, Subscription);
            }
            await CurrentMedicationService.UpdateMedicationListAsync(deleteList, OrgID, Subscription);
            await CurrentMedicationService.UpdateMedicationListAsync(medications, OrgID, Subscription);
            deleteList.Clear();
            AddList.Clear();
            editorContent = string.Join("<p>",
                medications.OrderByDescending(m => m.StartDate)
                .Select(m => $"Brand Name: {m.BrandName}, Composition: {m.DrugDetails}, Quantity: {m.Quantity}, " +
                             $"Start Date: {(m.StartDate.HasValue ? m.StartDate.Value.ToString("MM-dd-yyyy") : "")}, " +
                             $"End Date: {(m.EndDate.HasValue ? m.EndDate.Value.ToString("MM-dd-yyyy") : "")}"));

            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>

        private async Task CancelChanges()
        {
            deleteList.Clear();
            AddList.Clear();
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Open Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _currentmedicdialog.ShowAsync();
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            ResetInputFields();
            _currentmedicdialog.CloseAsync();
        }


        public enum Source { FDB, RxNorm }
        private string selectedDatabase = Source.RxNorm.ToString();

        private List<RxNormConcept> RxMedication { get; set; } = new();
        private RxNormConcept selectedRxMedication;

        private List<RxNormConcept> RxSBDC { get; set; } = new();
        private RxNormConcept selectedRxSBDC;

        private List<FDBMedicationName> FDBmedications { get; set; } = new();
        private FDBMedicationName selectedMedication;

        private List<FDBRoutedMedication> RoutedMedications { get; set; } = new();
        private FDBRoutedMedication selectedRoutedMedication;

        private List<FDBRoutedDosageFormMedication> RoutedDosageFormMedications { get; set; } = new();
        private FDBRoutedDosageFormMedication selectedRoutedDosageFormMedication;

        private List<FDBMedication> FinalMedications { get; set; } = new();
        private FDBMedication selectedFinalMedication;

        //Search for MedicationNames
        private Task<IEnumerable<FDBMedicationName>> SearchMedications(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return Task.FromResult(FDBmedications.AsEnumerable());

            var result = FDBmedications
                .Where(m => m.MED_NAME.Contains(value, StringComparison.OrdinalIgnoreCase));

            return Task.FromResult(result);
        }



        //When Medication name is selected
        private async Task OnMedicationSelected(FDBMedicationName med)
        {
            selectedMedication = med;
            selectedRoutedMedication = null;
            RoutedMedications.Clear();
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.MED_NAME_ID))
            {
                RoutedMedications = await FDBService.GetFDBRoutedMedications(med.MED_NAME_ID);
            }
        }

        //When Route is selected
        private async Task OnRoutedMedicationSelected(FDBRoutedMedication med)
        {
            selectedRoutedMedication = med;
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_MED_ID))
            {
                RoutedDosageFormMedications = await FDBService.GetFDBRoutedDosageFormMedications(med.ROUTED_MED_ID);
            }
        }

        //When Dosage Form is selected
        private async Task OnRoutedDosageFormMedicationSelected(FDBRoutedDosageFormMedication med)
        {
            selectedRoutedDosageFormMedication = med;
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_DOSAGE_FORM_MED_ID))
            {
                FinalMedications = await FDBService.GetFDBFinalMedications(med.ROUTED_DOSAGE_FORM_MED_ID);
            }
        }

        private async Task OnFinalMedicationSelected(FDBMedication med)
        {
            selectedFinalMedication = med;
        }


        private string GetRouteName(string routeIdStr)
        {
            if (string.IsNullOrEmpty(routeIdStr))
                return string.Empty;

            if (routeNameLookup.TryGetValue(routeIdStr, out string routeName))
                return routeName;

            return $"Route {routeIdStr}";
        }

        private string GetTakeName(string takeIdStr)
        {
            if (string.IsNullOrEmpty(takeIdStr))
                return string.Empty;

            if (takeNameLookup.TryGetValue(takeIdStr, out string takeName))
                return takeName;

            return $"Route {takeIdStr}";
        }

    }
}
﻿using Markdig;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;

namespace TeyaWebApp.Components.Pages
{
    public partial class Encounters : ComponentBase
    {

        [Inject]
        private ActiveUser User { get; set; }
        [Inject]
        private PatientService _PatientService { get; set; }

        private List<Record> records;
        private Guid? OrgID { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
        };
        private readonly string[] speakerColorPalette = new[] {
        "#4285F4", // Blue
        "#EA4335", // Red
        "#34A853", // Green 
        "#FBBC05", // Yellow
        "#8E24AA", // Purple
        "#00ACC1", // Cyan
        "#FB8C00", // Orange
        "#607D8B", // Blue Grey
        "#D81B60", // Pink
        "#1E88E5", // Light Blue
        "#43A047", // Light Green
        "#6D4C41"  // Brown
    };

        private string GetSpeakerColor(int index)
        {
            return speakerColorPalette[index % speakerColorPalette.Length];
        }
        private string GetBubbleColor(string color)
        {
            if (color.StartsWith("#"))
            {
                try
                {
                    return $"{color}33"; 
                }
                catch
                {
                    return "#f5f5f5";
                }
            }
            return "#f5f5f5";
        }
        protected override async Task OnInitializedAsync()
        {
           
            if(_PatientService.PatientData != null)
            {
                PatientID = _PatientService.PatientData.Id;
                OrgID = _PatientService.PatientData.OrganizationID;
                records = await ProgressNotesService.GetRecordsByPatientIdAsync(PatientID.Value, OrgID, false);
            }
            else
            {
                records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id), OrgID, false);
            }

        }
        private string ConvertToHtml(string markdown)
        {
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            var ConvertedString = Markdown.ToHtml(markdown, pipeline);
            return ConvertedString;
        }

        private string GetAudioUrl(Guid id)
        {
            return $"{Environment.GetEnvironmentVariable(Localizer["AudioUrl"])}/{id}.{Localizer["wav"]}";
        }

        
    }
}
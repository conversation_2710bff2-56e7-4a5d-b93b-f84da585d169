﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Net.NetworkInformation;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Examinations : ComponentBase
    {
        private SfRichTextEditor RichTextEditor;
        private SfGrid<Examination> ExaminationGrid;
        private MudDialog _examinationDialog;
        private List<Examination> activeExaminationEntries = new List<Examination>();
        private List<Examination> entriesToAdd = new List<Examination>();
        private List<Examination> entriesToUpdate = new List<Examination>();
        private List<Examination> entriesToDelete = new List<Examination>();
        private List<Examination> previousExaminationEntries = new List<Examination>();

        [Inject] public PatientService _PatientService { get; set; }
        [Inject] public ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IExaminationService ExaminationService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        private string editorContent;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        protected override async Task OnInitializedAsync() => await LoadExaminationEntriesAsync();

        private async Task LoadExaminationEntriesAsync()
        {
            try
            {
                PatientId = _PatientService.PatientData.Id;
                OrgID = _PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                activeExaminationEntries = await ExaminationService.GetExaminationByPatientIdAsyncAndIsActive(PatientId, OrgID, Subscription);
                previousExaminationEntries = activeExaminationEntries.Select(CloneExamination).ToList();
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Failed to load entries: {ex.Message}", Severity.Error);
            }
        }

        private Examination CloneExamination(Examination exam) => new Examination
        {
            ExaminationId = exam.ExaminationId,
            OrganizationId = exam.OrganizationId,
            PCPId = exam.PCPId,
            PatientId = exam.PatientId,
            CreatedDate = exam.CreatedDate,
            UpdateDate = exam.UpdateDate,
            GeneralDescription = exam.GeneralDescription,
            HEENT = exam.HEENT,
            Lungs = exam.Lungs,
            Abdomen = exam.Abdomen,
            PeripheralPulses = exam.PeripheralPulses,
            Skin = exam.Skin,
            Others = exam.Others,
            IsActive = exam.IsActive
        };

      
        

        public void ActionCompleteHandler(ActionEventArgs<Examination> args)
        {

           
            /// <summary>
            /// Add a new Record
            /// </summary>

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                var newEntry = new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    OrganizationId = Guid.Parse(User.id),
                    PCPId = Guid.Parse(User.id),
                    PatientId = PatientId,
                    CreatedDate = DateTime.Now,
                    UpdateDate = DateTime.Now,
                    GeneralDescription = null,
                    HEENT = null,
                    Lungs = null,
                    Abdomen = null,
                    PeripheralPulses = null,
                    Skin = null,
                    Others = null,
                    IsActive = true
                };
                entriesToAdd.Add(newEntry);
                activeExaminationEntries.Add(newEntry);
                StateHasChanged();
                ExaminationGrid.Refresh();

            }

        }

        public async Task ActionBeginHandler(ActionEventArgs<Examination> args)
        {

          

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                entriesToDelete.Add(args.Data);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {


                // Validated all the fields should take only Aphabates and spces
                if (!string.IsNullOrEmpty(args.Data.GeneralDescription) && !IsAlphabeticWithSpaces(args.Data.GeneralDescription))
                {
                    Snackbar.Add(@Localizer["Validation.GeneralDescriptionAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.HEENT) && !IsAlphabeticWithSpaces(args.Data.HEENT))
                {
                    Snackbar.Add(@Localizer["Validation.HEENTAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Lungs) && !IsAlphabeticWithSpaces(args.Data.Lungs))
                {
                    Snackbar.Add(@Localizer["Validation.LungsAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Abdomen) && !IsAlphabeticWithSpaces(args.Data.Abdomen))
                {
                    Snackbar.Add(@Localizer["Validation.AbdomenAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.PeripheralPulses) && !IsAlphabeticWithSpaces(args.Data.PeripheralPulses))
                {
                    Snackbar.Add(@Localizer["Validation.PeripheralPulsesAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Skin) && !IsAlphabeticWithSpaces(args.Data.Skin))
                {
                    Snackbar.Add(@Localizer["Validation.SkinAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }


                var exam = args.Data;
                exam.UpdateDate = DateTime.Now;
                entriesToUpdate.Add(exam);
                UpdateEditorContent();
                StateHasChanged();
            }


        }

        /// <summary>
        /// Method to Validates the field
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s]+$");
        }


        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }


        private void UpdateEditorContent()
        {
            var latestEntry = activeExaminationEntries
                .OrderByDescending(e => e.UpdateDate)
                .FirstOrDefault();

            editorContent = latestEntry != null
                ? $"<p><strong>General Description:</strong> {latestEntry.GeneralDescription ?? "N/A"}</p>" +
                  $"<p><strong>HEENT:</strong> {latestEntry.HEENT ?? "N/A"}</p>" +
                  $"<p><strong>Lungs:</strong> {latestEntry.Lungs ?? "N/A"}</p>" +
                  $"<p><strong>Abdomen:</strong> {latestEntry.Abdomen ?? "N/A"}</p>" +
                  $"<p><strong>Peripheral Pulses:</strong> {latestEntry.PeripheralPulses ?? "N/A"}</p>" +
                  $"<p><strong>Skin:</strong> {latestEntry.Skin ?? "N/A"}</p>" +
                  $"<p><strong>Others:</strong> {latestEntry.Others ?? "N/A"}</p>"
                : "<p>No data available</p>";

            RichTextEditor?.RefreshUIAsync();
        }

        /// <summary>
        /// This will Made Permanent Changes to the Database
        /// </summary>
        /// <returns></returns>

        private async Task SaveChanges()
        {
            try
            {
                if (entriesToAdd.Any())
                {
                    await ExaminationService.AddExaminationAsync(entriesToAdd, OrgID, Subscription);
                    entriesToAdd.Clear();
                }

                if (entriesToUpdate.Any())
                {
                    await ExaminationService.UpdateExaminationListAsync(entriesToUpdate, OrgID, Subscription);
                    entriesToUpdate.Clear();
                }

                if (entriesToDelete.Any())
                {
                    await ExaminationService.UpdateExaminationListAsync(entriesToDelete, OrgID, Subscription);
                    entriesToDelete.Clear();
                }

                await LoadExaminationEntriesAsync();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                _examinationDialog?.CloseAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Failed to save changes: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Cancel all the Changes and back to its previous state
        /// </summary>
        private void CancelChanges()
        {
            activeExaminationEntries = previousExaminationEntries.Select(CloneExamination).ToList();
            entriesToAdd.Clear();
            entriesToUpdate.Clear();
            entriesToDelete.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            _examinationDialog?.CloseAsync();
            UpdateEditorContent();
        }

        public List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel { Command = ToolbarCommand.Bold },
            new ToolbarItemModel { Command = ToolbarCommand.Italic },
            new ToolbarItemModel { Command = ToolbarCommand.Underline },
            new ToolbarItemModel { Command = ToolbarCommand.FontName },
            new ToolbarItemModel { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel { Command = ToolbarCommand.Undo },
            new ToolbarItemModel { Command = ToolbarCommand.Redo },
            new ToolbarItemModel { Name = "edit" }
        };

        private void OpenExaminationDialog()
        {
            _examinationDialog?.ShowAsync();
        }

    }
}



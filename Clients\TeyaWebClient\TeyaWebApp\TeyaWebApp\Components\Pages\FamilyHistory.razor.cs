﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using static TeyaWebApp.Components.Pages.Notes;
using static MudBlazor.Icons.Custom;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Net.Http;
using Syncfusion.Blazor.DropDowns;
using Microsoft.AspNetCore.Http.HttpResults;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class FamilyHistory : ComponentBase
    {
        private MudDialog _addMemberDialog;
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private List<string> ToolbarItems = new List<string> { "Add" };
        public SfGrid<FamilyMember> familyGrid { get; set; }
        public FamilyMember newVal = new();
        public List<Relations> relationList = new(); 
        public List<string> relationNames = new();
        private List<FamilyMember> FamilyMemberList { get; set; }
        private List<FamilyMember> newFamilyMemberList { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        private Guid? organizationId { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ILogger<Config> Logger { get; set; }
        private bool add = false;
        private List<FamilyMember> DeleteList = new();
        private List<FamilyMember> AddList = new();

        /// <summary>
        /// get list of Family Members Record from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try 
            {
                PatientID = _PatientService.PatientData.Id;
                organizationId = _PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                FamilyMemberList = await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID, organizationId, Subscription);
                ViewHandler();
                relationList = await RelationService.GetRelationsAsync();
                relationNames = relationList.Select(r => r.Relation).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        /// <summary>
        /// Auto complete for Relation Field in SF Grid
        /// </summary>
        private RenderFragment<object> RelationEditTemplate => context => builder =>
        {
            var familyMember = context as FamilyMember;
            if (familyMember == null) return;

            builder.OpenComponent<SfAutoComplete<string, string>>(0);
            builder.AddAttribute(1, "DataSource", relationNames);
            builder.AddAttribute(2, "Value", familyMember.Relation);
            builder.AddAttribute(3, "Placeholder", "Select Relation");
            builder.AddAttribute(4, "TValue", typeof(string));
            builder.AddAttribute(5, "TItem", typeof(string));
            builder.AddAttribute(6, "ValueChanged", EventCallback.Factory.Create<string>(this, value => familyMember.Relation = value));
            builder.AddAttribute(7, "AllowCustom", true);
            builder.AddAttribute(8, "ShowClearButton", true);
            builder.CloseComponent();
        };

        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>
        private void ActionCompletedHandler(ActionEventArgs<FamilyMember> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedRecord = args.Data as FamilyMember;
                    var existingItem = AddList.FirstOrDefault(v => v.RecordID == deletedRecord.RecordID);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedRecord.IsActive = false;
                        deletedRecord.UpdatedBy = Guid.Parse(User.id);
                        deletedRecord.UpdatedDate = DateTime.Now;
                        DeleteList.Add(deletedRecord);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.PatientId = PatientID;
                args.Data.OrganizationID = organizationId;
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = PatientID;
                args.Data.RecordID = new Guid();
                args.Data.IsActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals);
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
             _addMemberDialog.ShowAsync();
        }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add", TooltipText = "Add Medication" },
        };

        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "";

            foreach (var member in FamilyMemberList)
            {
                str += $"<li><b>{member.Relation}:</b> {member.Status}</li>";
            }

            str += "</ul>";

            editorContent = str;
        }

        /// <summary>
        /// Add Family Member to database
        /// </summary>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await FamilyMemberService.CreateMemberAsync(AddList, organizationId, Subscription);
            }
            await FamilyMemberService.UpdateFamilyMemberList(DeleteList, organizationId, Subscription);
            await FamilyMemberService.UpdateFamilyMemberList(FamilyMemberList, organizationId, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            FamilyMemberList = await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID, organizationId, Subscription);

            ViewHandler();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _addMemberDialog.CloseAsync();
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>
        private async Task CancelChanges()
        {
            DeleteList.Clear();
            AddList.Clear();
            FamilyMemberList = await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID, organizationId, Subscription);
            await InvokeAsync(StateHasChanged);
            _addMemberDialog.CloseAsync();
        }

    }
}
﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class MedicalHistory
    {
        [Inject] ISnackbar Snackbar { get; set; }
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IMedicalHistoryService _MedicalService { get; set; }

        [Inject] private PatientService _PatientService { get; set; }
        private MudDialog _medicalhistory;
        private DateTime? _CreatedDate;
        private Guid? OrgID { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private List<MedicalHistoryDTO> _localData { get; set; }
        private List<MedicalHistoryDTO> medicalhistory { get; set; }
        public string ICDName { get; set; }
        private SfRichTextEditor RichTextEditor;
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        public SfGrid<MedicalHistoryDTO> MedicalGrid { get; set; }
        private Guid PatientId { get; set; }
        private string editorContent;
        private List<MedicalHistoryDTO> deleteMedicalHistorylist { get; set; } = new List<MedicalHistoryDTO>();
        [Inject] private IDialogService DialogService { get; set; }
        private List<MedicalHistoryDTO> CancelCount { get; set; } = new List<MedicalHistoryDTO>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };

        

        /// <summary>
        /// Handles the backdrop click event and displays a localized snackbar message.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        List<MedicalHistoryDTO> Count1,Count2;
        protected override async Task OnInitializedAsync()
        {
                PatientId = _PatientService.PatientData.Id;
                OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            _icdCodes = await _ICDService.GetAllICDCodesAsync();
                medicalhistory = await _MedicalService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
                _localData = medicalhistory;
                editorContent = string.Join("<br>", medicalhistory.Select(m =>
                $" <strong>Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])},</strong> " +
                $"History: {(string.IsNullOrEmpty(m.History) ? Localizer["EnterHistory"] : m.History)}"
                ));
        }
        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
 
        private async Task OpenNewDialogBox()
        {
            await _medicalhistory.ShowAsync();
        }
 
        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
 
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _medicalhistory.CloseAsync();
        }
 
        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
 
        protected async Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            var searchResults = _icdCodes
                .Where(icd => (icd.Code != null && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                              (icd.Description != null && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase)))
                .Select(icd => $"{icd.Code} - {icd.Description ?? Localizer["NoDescriptionAvailable"]}")
                .ToList();
            cancellationToken.ThrowIfCancellationRequested();
            return searchResults;
        }


        /// <summary>
        /// List to store newly added medical history records.
        /// </summary>
        public List<MedicalHistoryDTO> AddList = new List<MedicalHistoryDTO>();

        /// <summary>
        /// Adds a new medical history entry to the list and refreshes the grid.
        /// </summary>
        private async void AddNewHistory()
        {
            var newHistory = new MedicalHistoryDTO
            {
                MedicalHistoryID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = PatientId,
                OrganizationId = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                History = ICDName,
                IsActive = true,
            };

            AddList.Add(newHistory);
            medicalhistory.Add(newHistory);
            await MedicalGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Resets input fields to their default values.
        /// </summary>
        private void ResetInputFields()
        {
            ICDName = string.Empty;
            _CreatedDate = null;
        }

        /// <summary>
        /// List to store deleted medical history records.
        /// </summary>
        public List<MedicalHistoryDTO> deleteList = new List<MedicalHistoryDTO>();

        /// <summary>
        /// Handles the action completion event for medical history records.
        /// If an item is deleted, it is either removed from the add list or marked as inactive and added to the delete list.
        /// </summary>
        /// <param name="args">Event arguments containing action details.</param>
        public void ActionCompletedHandler(ActionEventArgs<MedicalHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedMedicalHistory = args.Data as MedicalHistoryDTO;
                var existingItem = AddList.FirstOrDefault(m => m.MedicalHistoryID == deletedMedicalHistory.MedicalHistoryID);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }


        public async Task ActionBeginHandler(ActionEventArgs<MedicalHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                        Localizer["ConfirmDelete"],
                        Localizer["DeleteConfirmationMessage"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["No"]);


                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
            }
        }
        /// <summary>
        /// Cancels the operation, clears lists, reloads data, and resets input fields.
        /// </summary>
        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            medicalhistory = await _MedicalService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Saves the medical history data by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _MedicalService.AddMedicalHistoryAsync(AddList, OrgID, Subscription);
            }

            await _MedicalService.UpdateMedicalHistoryListAsync(medicalhistory, OrgID, Subscription);
            await _MedicalService.UpdateMedicalHistoryListAsync(deleteList, OrgID, Subscription);

            deleteList.Clear();
            AddList.Clear();

            editorContent = string.Join("<br>", medicalhistory.Select(m =>
            $"<strong> Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}, </strong>" +
            $"History: {(string.IsNullOrEmpty(m.History) ? Localizer["Enter History"] : m.History)}"
            ));

            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);

            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Handles changes to the ICD Name input field and updates the component state.
        /// </summary>
        /// <param name="value">The new value of the ICD Name.</param>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            await InvokeAsync(StateHasChanged);
        }

    }
}
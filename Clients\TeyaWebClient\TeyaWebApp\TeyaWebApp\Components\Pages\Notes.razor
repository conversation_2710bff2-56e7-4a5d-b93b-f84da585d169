﻿@page "/Notes"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject HttpClient Http
@using TeyaUIViewModels.ViewModel
@inject ISpeechService speechService
@inject IJSRuntime JSRuntime
@inject ITokenService TokenService
@inject IProgressNotesService ProgressNotesService
@using System.Collections.Generic


@if(AICard){
    <MudGrid>
        <MudItem xs="12" Class="mx-auto">
            <MudCard Elevation="4" Class="mb-6 my-card">
                <MudCardHeader Class="d-flex justify-space-between align-center">
                    <div>
                        <MudText Typo="Typo.h6">Patient Name : @records[0].PatientName</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">Record Date: @records[0].DateTime.ToString("g")</MudText>
                    </div>
                    <audio controls>
                        <source src="@GetAudioUrl(records[0].Id)" type="audio/mp3" />
                        Your browser does not support the audio tag.
                    </audio>
                </MudCardHeader>
                <MudCardContent Class="p-4">
                    <div class="d-flex">
                        <!-- First Column: Notes -->
                        <div class="flex-fill me-2">
                            @foreach (var notes in parsedNotes)
                            {
                                <!-- Notes.Key Styled Similar to Transcription Heading -->
                                <MudText Typo="Typo.subtitle2" Class="mb-2" Style="font-size : 18px">
                                    @notes.Key
                                </MudText>
                                <MudPaper Class="pa-2 mb-3" Elevation="2">
                                    @foreach (var note in notes.Value)
                                    {
                                        <!-- Subheading Styled Like Transcription -->
                                        <MudText Typo="Typo.h6" Class="mb-1 mt-5" Style="font-size : 15px">
                                            @note.Key
                                        </MudText>
                                        <div class="custom-rte-container">
                                            <SfRichTextEditor Value="@ConvertToHtml(notes.Value[note.Key])"
                                                              ValueChanged="@((string newValue) => HandleRichTextEditorChange(notes.Key, note.Key, newValue))"
                                                              Key="@($"{notes.Key}_{note.Key}")">
                                                <RichTextEditorToolbarSettings Items="@Tools">
                                                </RichTextEditorToolbarSettings>
                                            </SfRichTextEditor>
                                        </div>
                                    }
                                </MudPaper>
                            }
                        </div>
                        <!-- Second Column: Transcription -->
                        <div class="second-column ms-auto" style="width: 40%;">
                            <MudText Typo="Typo.subtitle2" Class="mb-2" Style="font-size : 18px">
                                @Localizer["Transcription"]
                            </MudText>
                            <MudPaper Class="pa-4">
                                <div class="conversation-container">
                                    @if (!string.IsNullOrEmpty(records[0].Transcription))
                                    {
                                        var lines = records[0].Transcription.Split('\n');
                                        string currentSpeaker = "";
                                        Dictionary<string, string> speakerColors = new Dictionary<string, string>();
                                        Dictionary<string, int> speakerPositions = new Dictionary<string, int>();
                                        int speakerCount = 0;

                                        @foreach (var line in lines)
                                        {
                                            if (string.IsNullOrWhiteSpace(line))
                                            {
                                                continue;
                                            }

                                            // Check if line starts with speaker identifier
                                            var parts = line.Split(new[] { ":" }, 2, StringSplitOptions.None);
                                            if (parts.Length == 2)
                                            {
                                                var speaker = parts[0].Trim();
                                                var message = parts[1].Trim();
                                                if (speaker.Equals("Unknown", StringComparison.OrdinalIgnoreCase))
                                                {
                                                    continue;
                                                }
                                                currentSpeaker = speaker;

                                                // Assign color and position if this is a new speaker
                                                if (!speakerColors.ContainsKey(speaker))
                                                {
                                                    speakerColors[speaker] = GetSpeakerColor(speakerCount);
                                                    speakerPositions[speaker] = speakerCount % 2; // 0 = left, 1 = right
                                                    speakerCount++;
                                                }

                                                var alignment = speakerPositions[speaker] == 0 ? "left-align" : "right-align";
                                                string speakerColor = speakerColors[speaker];
                                                string bubbleColor = GetBubbleColor(speakerColor);

                                                <div class="conversation-message @alignment">
                                                    <div class="speaker-name-container" style=@($"color: {speakerColor}")>
                                                        <MudText Typo="Typo.subtitle2" Class="speaker-name">@speaker</MudText>
                                                    </div>
                                                    <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                        <MudText Style="text-align: left;">@message</MudText>
                                                    </MudPaper>
                                                </div>
                                            }
                                            else
                                            {
                                                // If no speaker identified, use the current speaker's alignment
                                                if (!string.IsNullOrEmpty(currentSpeaker) && speakerPositions.ContainsKey(currentSpeaker))
                                                {
                                                    var alignment = speakerPositions[currentSpeaker] == 0 ? "left-align" : "right-align";
                                                    string bubbleColor = GetBubbleColor(speakerColors[currentSpeaker]);

                                                    <div class="conversation-message @alignment">
                                                        <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                            <MudText Style="text-align: left;">@line</MudText>
                                                        </MudPaper>
                                                    </div>
                                                }
                                                else
                                                {
                                                    // Fallback for lines without a speaker context
                                                    <div class="conversation-message left-align">
                                                        <MudPaper Elevation="0" Class="message-bubble" Style="background-color: #f5f5f5">
                                                            <MudText Style="text-align: left;">@line</MudText>
                                                        </MudPaper>
                                                    </div>
                                                }
                                            }
                                        }
                                    }
                                </div>
                            </MudPaper>
                        </div>
                    </div>
                </MudCardContent>
            </MudCard>
            <MudButton OnClick="LockRecord">Lock</MudButton>
        </MudItem>
    </MudGrid>
}
else{
    @if (@_PatientService.PatientData != null)
    {
        @if (productVisibility)
        {
            <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="ShowMicrophone" Class="mx-2 mt-2">@Localizer["Teya AI"] </MudButton>
            <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenTreatmentDialogAsync" Class="mx-2 mt-2">@Localizer["Treatment"] </MudButton>
        }
        <QualityMeasure></QualityMeasure>
        <div class="py-4">

            <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
                <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 12px;">
                    <strong>@Localizer["Visit Type"]</strong>: @_PatientService.VisitType
                </p>

                <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 12px;">
                    <strong>@Localizer["Template Name"]</strong>: @TemplateName
                </p>
                <MudGrid>
                    <MudItem xs="12" Class="mx-auto">
                        <MudCard Elevation="4" Class="mb-6 my-card">
                            <MudCardContent Class="p-4">
                                <div class="d-flex">
                                    <div class="flex-fill me-2">
                                        @foreach (var section in NotesData)
                                        {
                                            @foreach (var kvp in section)
                                            {
                                                <p class="section-heading"><strong>@kvp.Key</strong></p>

                                                @foreach (var data in kvp.Value)
                                                {
                                                    <MudCard Class="mt-3 pa-4">
                                                        <p class="subsection-heading"><strong>@data.Key</strong></p>

                                                        @if (ListDetails.Any(d => d.Name.Replace(" ", "").Equals(data.Key.Replace(" ", ""), StringComparison.OrdinalIgnoreCase)))
                                                        {
                                                            var componentType = GetComponentType(data.Key.Replace(" ", ""));


                                                            if (componentType != null)
                                                            {
                                                                <DynamicComponent Type="componentType" />
                                                            }
                                                            else
                                                            {
                                                                <SfRichTextEditor Value="@editorContent">
                                                                    <RichTextEditorToolbarSettings Items="@Tools">
                                                                    </RichTextEditorToolbarSettings>
                                                                </SfRichTextEditor>
                                                            }
                                                        }
                                                        else
                                                        {
                                                            <SfRichTextEditor Value="@editorContent">
                                                                <RichTextEditorToolbarSettings Items="@Tools">
                                                                </RichTextEditorToolbarSettings>
                                                            </SfRichTextEditor>
                                                        }
                                                    </MudCard>
                                                }
                                            }
                                        }
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudContainer>
        </div>
    }
else{
    <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="ShowMicrophone" Class="mx-2 mt-2">@Localizer["Teya AI"] </MudButton>
    <QualityMeasure></QualityMeasure>
        <div class="py-4">

            <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
                <MudGrid>
                    <MudItem xs="12" Class="mx-auto">
                        <MudCard Elevation="4" Class="mb-6 my-card">
                            <MudCardContent Class="p-4">
                                <div class="d-flex">
                                    <div class="flex-fill me-2">
                                        @foreach (var section in NotesData)
                                        {
                                            @foreach (var kvp in section)
                                            {
                   
                                                <MudText Typo="Typo.subtitle2" Class="mb-2" Style="font-size: 18px">
                                                    @kvp.Key
                                                </MudText>
                                                <MudPaper Class="pa-2 mb-3" Elevation="2">
                                                    @foreach (var data in kvp.Value)
                                                    {
                      
                                                        <MudText Typo="Typo.h6" Class="mb-1 mt-5" Style="font-size: 15px">
                                                            @data.Key
                                                        </MudText>
                                                        <div class="custom-rte-container">
                                                            <SfRichTextEditor Value="@editorContent">
                                                                <RichTextEditorToolbarSettings Items="@Tools">
                                                                </RichTextEditorToolbarSettings>
                                                            </SfRichTextEditor>
                                                        </div>
                                                    }
                                                </MudPaper>
                                            }
                                        }
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudContainer>
        </div>
    }

}




<MudDialog Class="recording-dialog" @ref="MicrophoneDialog" OnBackdropClick="HandleBackdropClick"
           Style="width: 500px; border-radius: 12px; overflow: hidden; background: white;">
    <DialogContent>
        <div class="dialog-header">
            <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Error" Size="Size.Small" OnClick="CloseMicrophoneDialog" />
        </div>
        <div class="recording-container">
            @if (isLoading)
            {
                <!-- Processing state -->
                <div class="processing-indicator">
                    <span class="material-icons spin">@Localizer["autorenew"]</span>
                    <div class="processing-text">@Localizer["Processing..."]</div>
                </div>
            }
            else if (isRecorderActive)
            {
                <!-- Recording state -->
                <div class="recording-indicator-start" style="@(isPaused ? "background-color: #2bcbba;" : "")">
                    @(isPaused ? "Paused" : "Recording...")
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetBarStyle(i)"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer-recording">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton StartIcon="@(isPaused ? Icons.Material.Filled.PlayArrow : Icons.Material.Filled.Pause)"
                               OnClick="OnPauseIconClick"
                               Class="end-visit-btn">
                        @Localizer[isPaused ? "Resume" : "Pause"]
                    </MudButton>

                    <MudButton Class="end-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.StopCircle">
                        @Localizer["Stop"]
                    </MudButton>
                </div>
            }
            else
            {
                <!-- Initial state -->
                <div class="recording-indicator" style="background-color: red;">
                    @Localizer["Teya Ai Scribe"]
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetNormalBarStyle()"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton Class="start-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.Mic">
                        @Localizer["Start"]
                    </MudButton>
                </div>
            }
        </div>
    </DialogContent>
</MudDialog>

<style>
    .dialog-header {
        display: flex;
        justify-content: flex-end;
    }

    .my-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: rgba(235, 225, 216, 1);
        width: 100%;
    }

        .my-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

    .first-column {
        display: flex;
        flex-direction: column;
    }

    .second-column {
        width: 400px;
        min-width: 300px;
    }

    .mud-card-content {
        padding: 16px;
    }

    .conversation-container {
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
    }

    .conversation-message {
        display: flex;
        flex-direction: column;
        max-width: 80%;
    }

    .left-align {
        align-self: flex-start;
    }

    .right-align {
        align-self: flex-end;
    }

        .right-align .speaker-name-container {
            align-self: flex-end;
        }

        .right-align .message-bubble {
            margin-left: auto;
        }

    .speaker-name {
        margin-bottom: 4px;
        font-weight: bold;
    }

    .message-bubble {
        padding: 8px;
        border-radius: 12px;
        word-break: break-word;
    }

    /* Extra styling for better readability */
    .left-align .message-bubble {
        border-top-left-radius: 2px;
    }

    .right-align .message-bubble {
        border-top-right-radius: 2px;
    }
    .custom-rte-container {
        position: relative;
        transition: margin-top 0.3s ease;
    }

        .custom-rte-container .e-rte-toolbar {
            display: none;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            border-radius: 4px;
            margin-bottom: 0;
        }

        .custom-rte-container:hover .e-rte-toolbar {
            display: block;
        }

        .custom-rte-container .e-rte-toolbar-wrapper {
            background-color: white;
            border-radius: 4px;
        }

        
</style>


using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Microsoft.JSInterop;
using MudBlazor;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Timers;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.TeyaAIScribeResources;
using static Microsoft.Azure.Amqp.CbsConstants;

namespace TeyaWebApp.Components.Pages
{
    public partial class Notes : ComponentBase
    {
        private bool isLoading { get; set; }
        private Random random = new Random();
        private bool isPaused = false;
        private System.Threading.Timer animationTimer;
        private bool AICard { get; set; }
        private bool toggleCard = true;
        private bool productVisibility;
        [Inject]
        private ILogger<Notes> logger { get; set; }
        private List<Record> records;
        private List<Dictionary<string, Dictionary<string, string>>> NotesData = new List<Dictionary<string, Dictionary<string, string>>>();
        [Inject] ITemplateService templateService { get; set; }
        private HashSet<string> sectionNames = new(); // HashSet to prevent duplicates
        private HashSet<string> RenderedSections = new(); // Tracks rendered UI sections
        private List<TemplateData> ProviderData = new();
        private MudDialog MicrophoneDialog;
        private Type componentType { get; set; }
        [Inject] private ISoapNotesComponentsService SoapNotesComponentsService { get; set; }
        [Inject] private ILogger<Notes> Logger { get; set; }
        private IEnumerable<SoapNotesComponent> ListDetails = new List<SoapNotesComponent>();
        [Inject]
        private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private Guid? OrgID { get; set; }

        private Dictionary<string, Dictionary<string, string>> parsedNotes;
        private bool hasChanges = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
        };
        private string editorContent;
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid? PatientID { get; set; }
        private string TemplateName { get; set; }
        private bool isAnimating = false;
        private bool isRecorderActive = false;
        private bool isPausedd = false;
        private IEnumerable<Speech> Speeches = new List<Speech>();
        private string? CurrentSpeechText { get; set; }
        private string MicIcon => isRecorderActive ? Localizer["stop_circle"] : Localizer["mic"];
        private string PauseResumeIcon => isPausedd ? Localizer["play_arrow"] : Localizer["pause"];
        private System.Timers.Timer callDurationTimer;
        private int callDuration = 0;

        private readonly string[] speakerColorPalette = new[] {
        "#4285F4", // Blue
        "#EA4335", // Red
        "#34A853", // Green
        "#FBBC05", // Yellow
        "#8E24AA", // Purple
        "#00ACC1", // Cyan
        "#FB8C00", // Orange
        "#607D8B", // Blue Grey
        "#D81B60", // Pink
        "#1E88E5", // Light Blue
        "#43A047", // Light Green
        "#6D4C41"  // Brown
    };

        private string GetSpeakerColor(int index)
        {
            return speakerColorPalette[index % speakerColorPalette.Length];
        }
        private string GetBubbleColor(string color)
        {
            if (color.StartsWith("#"))
            {
                try
                {
                    return $"{color}33";
                }
                catch
                {
                    return "#f5f5f5";
                }
            }
            return "#f5f5f5";
        }
        protected override async Task OnInitializedAsync()
        {
            productVisibility = await ToggleTeyaAIVisibility();   //check product visibility of Provider, Need to be done
            OrgID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];

            if (_PatientService.PatientData != null)
            {

                //When patient is selected

                PatientID = _PatientService.PatientData.Id;
                OrgID = _PatientService.PatientData.OrganizationID;
                ListDetails = await SoapNotesComponentsService.GetAllDetailsAsync();
                ProviderData = await templateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                var defaultTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == _PatientService.VisitType);
                if (defaultTemplate == null)
                {
                    var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                    defaultTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                }
                TemplateName = defaultTemplate.TemplateName;
                if (defaultTemplate != null && !string.IsNullOrEmpty(defaultTemplate.Template))
                {
                    try
                    {
                        var templateData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(defaultTemplate.Template);
                        if (templateData != null && templateData.Count > 0)
                        {
                            sectionNames.Clear();
                            NotesData.Clear();
                            foreach (var section in templateData)
                            {
                                var heading = section.Key;
                                if (!NotesData.Any(s => s.ContainsKey(heading)))
                                {
                                    var innerDictionary = new Dictionary<string, string>();
                                    foreach (var key in section.Value.Keys)
                                    {
                                        if (sectionNames.Add(key))
                                        {
                                            innerDictionary[key] = "";
                                        }
                                    }
                                    NotesData.Add(new Dictionary<string, Dictionary<string, string>> { { heading, innerDictionary } });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"Error while parsing template JSON: {ex.Message}");
                    }
                }
                records = await ProgressNotesService.GetRecordsByPatientIdAsync(PatientID.Value, OrgID, Subscription);

                records = records.Where(record => record.isEditable).ToList();

                if (records.Count() == 0)
                {
                    //Manual mode
                    AICard = false;
                }
                else
                {
                    //AI card for Selected Patient

                    AICard = true;
                    parsedNotes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(
               JsonSerializer.Serialize(JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records[0].Notes)));
                }

            }

            else
            {
                //When no patient is selected
                var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                var defaultTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                var templateData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(defaultTemplate.Template);
                if (templateData != null && templateData.Count > 0)
                {
                    sectionNames.Clear();
                    NotesData.Clear();
                    foreach (var section in templateData)
                    {
                        var heading = section.Key;
                        if (!NotesData.Any(s => s.ContainsKey(heading)))
                        {
                            var innerDictionary = new Dictionary<string, string>();
                            foreach (var key in section.Value.Keys)
                            {
                                if (sectionNames.Add(key))
                                {
                                    innerDictionary[key] = "";
                                }
                            }
                            NotesData.Add(new Dictionary<string, Dictionary<string, string>> { { heading, innerDictionary } });
                        }
                    }

                }
                records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();

                if (records.Count() == 0)
                {
                    //Manual Mode, Set Default Template
                    AICard = false;
                }
                else
                {
                    //Latest AI card for Provider

                    AICard = true;
                    parsedNotes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(
               JsonSerializer.Serialize(JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records[0].Notes)));

                }
            }

        }


        private string ConvertToHtml(string markdown)
        {
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            return Markdown.ToHtml(markdown, pipeline);
        }
        private bool isAudioDetected = false;
        [JSInvokable]
        public async Task OnAudioDetected(bool isAudioDetect)
        {
            isAudioDetected = isAudioDetect;
            await InvokeAsync(StateHasChanged);
        }
        private string GetBarStyle(int index)
        {
            if (isPaused)
            {
                return "--height: 25px; --opacity: 0.5;";
            }

            if (!isAudioDetected)
            {
                return "--height: 25px;";
            }

            // Random height between 15 and 65 when audio is detected
            double height = random.Next(15, 75);
            return $"--height: {height}px;";
        }
        private async Task<bool> ToggleTeyaAIVisibility()
        {
            //bool access = await MemberService.HasProductAccess(Guid.Parse(User.id), Guid.Parse(Environment.GetEnvironmentVariable("ProductId"))); //add patient ID
            //return access;
            return true;
        }

        private string GetAudioUrl(Guid id)
        {
            return $"{Environment.GetEnvironmentVariable(Localizer["AudioUrl"])}/{id}.{Localizer["webm"]}";
        }

        private void HandleRichTextEditorChange(string category, string noteKey, string newValue)
        {
            if (!parsedNotes.ContainsKey(category))
            {
                parsedNotes[category] = new Dictionary<string, string>();
            }
            parsedNotes[category][noteKey] = newValue;
            hasChanges = true;
            StateHasChanged();
        }

        private async Task LockRecord()
        {
            records[0].isEditable = false;
            records[0].Notes = JsonSerializer.Serialize(parsedNotes);
            var response = await ProgressNotesService.SaveRecordAsync(records[0], OrgID, Subscription);
            if (response.IsSuccessStatusCode)
            {
                records[0] = null;
            }
            AICard = false;
            StateHasChanged();
        }

        Type GetComponentType(string componentName)
        {
            string fullTypeName = $"TeyaWebApp.Components.Pages.{componentName}";
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetType(fullTypeName);

        }
        private void ShowMicrophone()
        {
            MicrophoneDialog.ShowAsync();
        }

        private async Task CloseMicrophoneDialog()
        {
            isRecorderActive = false;
            StopAnimation();
            StopCallDurationTimer();
            await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.CancelRecord");
            MicrophoneDialog.CloseAsync();
        }
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["Backdrop-Disabled"], Severity.Info);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Task.Delay(int.Parse(Localizer["500"]));
                var accessToken = TokenService.AccessToken;
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.Initialize"], DotNetObjectReference.Create(this));
            }
        }
        private async Task OnMicIconClick()
        {
            isRecorderActive = !isRecorderActive;

            if (isRecorderActive)
            {
                StartAnimation();
                StartCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StartRecord"]);
                await speechService.StartTranscriptionAsync(Guid.NewGuid());
            }
            else
            {
                isLoading = true; // Show loading icon
                StateHasChanged(); // Force UI update
                StopAnimation();
                StopCallDurationTimer();

                Guid id = Guid.NewGuid();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StopRecord"], id, TokenService.AccessToken);
                if (_PatientService.PatientData != null)
                {
                    await speechService.StopTranscriptionAsync(id, _PatientService.PatientData.Id, _PatientService.VisitType, OrgID, Subscription);
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(_PatientService.PatientData.Id, OrgID, Subscription);
                    records = records.Where(record => record.isEditable).ToList();
                }
                else
                {
                    Guid NewPatientID = Guid.NewGuid();
                    await speechService.StopTranscriptionAsync(id, NewPatientID, _PatientService.VisitType, OrgID, Subscription);
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                    records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();
                }

                AICard = true;
                parsedNotes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(
                    JsonSerializer.Serialize(JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records[0].Notes)));
                isLoading = false; // Hide loading icon
                CloseMicrophoneDialog();
            }

            StateHasChanged();
        }

        protected override void OnInitialized()
        {
            animationTimer = new System.Threading.Timer(UpdateBars, null, 0, 100);
        }
        public async Task OpenTreatmentDialogAsync()
        {
            try
            {
                await DialogService.ShowAsync<TreatmentPage>("Treatment", new DialogOptions { MaxWidth = MaxWidth.ExtraLarge, FullWidth = true, CloseOnEscapeKey = true, CloseButton = true });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        private string GetRandomBarStyle()
        {
            if (isPaused)
            {
                int height = random.Next(15, 69);
                return $"--height: {height}px; --opacity: 0.5;";
            }

            int randomHeight = random.Next(32, 110);
            return $"--height: {randomHeight}px;";
        }
        private string GetNormalBarStyle()
        {
            int randomHeight = 25;
            return $"--height: {randomHeight}px;";
        }

        private void UpdateBars(object state)
        {
            if (!isPaused)
            {
                InvokeAsync(StateHasChanged);
            }
        }

        public void Dispose()
        {
            animationTimer?.Dispose();
        }
        [JSInvokable]
        public async Task OnRecordingComplete(string recordId)
        {
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnPauseIconClick()
        {
            isPaused = !isPaused;
            if (isPaused)
            {
                PauseAnimation();
                PauseCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.PauseRecord"]);
            }
            else
            {
                ResumeAnimation();
                ResumeCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.ResumeRecord"]);
            }

            StateHasChanged();
        }
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            await speechService.ProcessAudioChunk(base64AudioChunk);
        }
        private void StartAnimation()
        {
            isAnimating = true;
            isPaused = false;
            StateHasChanged();
        }

        private void StopAnimation()
        {
            isAnimating = false;
            StateHasChanged();
        }

        private void PauseAnimation()
        {
            isPaused = true;
            StopAnimation();
        }

        private void ResumeAnimation()
        {
            isPaused = false;
            StartAnimation();
        }

        private void StartCallDurationTimer()
        {
            callDuration = 0;
            callDurationTimer = new System.Timers.Timer(1000);
            callDurationTimer.Elapsed += UpdateCallDuration;
            callDurationTimer.Start();
        }

        private void UpdateCallDuration(object sender, ElapsedEventArgs e)
        {
            callDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void StopCallDurationTimer()
        {
            callDurationTimer?.Stop();
            callDurationTimer?.Dispose();
            callDuration = 0;
            StateHasChanged();
        }

        private string GetBarAnimationStyle(int index) =>
            isAnimating ? $"animation: wave-lg {new Random().NextDouble() * (0.7 - 0.2) + 0.2}s infinite ease-in-out alternate;" : "";

        private void PauseCallDurationTimer()
        {
            callDurationTimer?.Stop();
        }

        private void ResumeCallDurationTimer()
        {
            callDurationTimer?.Start();
        }

        private string FormatCallDuration(int durationInSeconds)
        {
            TimeSpan time = TimeSpan.FromSeconds(durationInSeconds);
            return time.ToString(@"mm\:ss");
        }
        public void OpenCustomImmunizationAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomImmunizationAlert>("Immunization Alerts", options);

                // The dialog will be closed by the CustomLabAlert component when Save or Cancel is clicked
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

    }
}
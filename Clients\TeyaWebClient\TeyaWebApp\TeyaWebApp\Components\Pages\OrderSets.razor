﻿@page "/OrderSets"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaUIViewModels.ViewModel
@using TeyaUIModels.Model
@using TeyaWebApp.ViewModel
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.RichTextEditor
@using MudBlazor
@using Syncfusion.Blazor.Navigations
@using System.ComponentModel.DataAnnotations

<div class="py-2">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pl-0 pt-3 pb-3 pr-3">
        <MudGrid Spacing="2">
            <MudItem xs="3">
                <MudPaper Class="pa-4" Style="height: 100vh;overflow-y: auto;">
                    <MudGrid Spacing="1">
                        <MudItem xs="12">
                            <MudAutocomplete T="string"
                                             Value="@selectedOrderSet"
                                             SearchFunc="SearchOrderSet"
                                             ToStringFunc="@( (SearchedOrderSet) => SearchedOrderSet)"
                                             Placeholder="@Localizer["Enter OrderSet Name"]"
                                             ValueChanged="@OnOrderSetSelected"
                                             Adornment="Adornment.End"
                                             CoerceText="true"
                                             Clearable="true"
                                             Dense="true"
                                             ResetValueOnEmptyText="true"
                                             Margin="Margin.Dense"
                                             Variant="Variant.Outlined"
                                             MinCharacters="2">
                            </MudAutocomplete>
                        </MudItem>
                        <MudItem xs="12" Class="mt-4">
                            <MudText Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Typo="Typo.h6" GutterBottom="true">
                                @Localizer["Available Order Sets"]
                            </MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudList Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" T="CompleteOrderSet" Dense="true">
                                @foreach (var orderSetItem in CompleteOrderSetList)
                                {
                                    <MudListItem @onclick="@(() => OnCompleteOrderSetClicked(orderSetItem))">
                                        @orderSetItem.orderSet.OrderSetName
                                    </MudListItem>
                                }
                            </MudList>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>
            <MudItem xs="9" >
                <MudPaper Class="pa-4" Style="height: 100vh; overflow-y: auto;">
                    <MudGrid Spacing="2">
                        <MudItem xs="12"><MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["New Order Set"]</MudText></MudItem>
                        <MudItem xs="12">
                            <MudGrid Spacing="2" Class="mb-1">
                                <MudItem xs="12">
                                    <MudPaper Class="pa-3 mt-1" >
                                        <MudGrid Spacing="2">
                                            <MudItem xs="4">
                                                <MudGrid Spacing="1">
                                                    <MudItem xs="4"><MudText Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Typo="Typo.body1">Order Set: </MudText></MudItem>
                                                    <MudItem xs="7"><SfTextBox @bind-Value="OrderSetName" Dense /></MudItem>
                                                    <MudItem xs="1"></MudItem>
                                                </MudGrid>
                                            </MudItem>
                                            <MudItem xs="8">
                                                <MudGrid Spacing="1">
                                                    <MudItem xs="2"><MudButton Style="height:32px;width:70px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Filled" OnClick="AddNewOrderSet">New</MudButton></MudItem>
                                                    <MudItem xs="2"><MudButton Style="height:32px;width:70px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Outlined" OnClick="UpdateOrderSet">Update</MudButton></MudItem>
                                                    <MudItem xs="2"><MudButton Style="height:32px;width:70px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Outlined">Merge</MudButton></MudItem>
                                                    <MudItem xs="2"><MudButton Style="height:32px;width:70px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Outlined" OnClick="DeleteOrderSet" Color="Color.Error">Delete</MudButton></MudItem>
                                                </MudGrid>
                                            </MudItem>
                                           
                                            <MudItem xs="4">
                                                <div style="display: flex; align-items: center;">
                                                    <MudItem xs="4"><MudText Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Typo="Typo.body1">Diagnosis Linked:</MudText></MudItem>
                                                        <MudItem xs="7">
                                                            <SfTextBox @bind-Value="DiagnosisLinked"
                                                                       CssClass="aligned-textbox"
                                                                       Dense />
                                                        </MudItem>
                                                        <MudItem xs="1"></MudItem>
                                                        
                                                </div>
                                            </MudItem>

                                        </MudGrid>
                                    </MudPaper>
                                </MudItem>
                            </MudGrid>
                        </MudItem>
                        <MudItem xs="12">
                                <MudGrid Spacing="1">
                                    <MudItem xs="12">
                                        @if(newOrderSet)
                                        {
                                            <div class="button-container">
                                            <MudButton Variant="Variant.Filled" OnClick="ToggleRXForm" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Class="toggle-button">
                                                    @Localizer["RX Details"]
                                                </MudButton>
                                            </div>

                                            @if (showRXForm)
                                            {
                                                <MudGrid Spacing="3" Style="align-items: center;">
                                                    <MudItem xs="4">
                                                        <MudAutocomplete T="string"
                                                                         Label="@Localizer["Search Brand Names"]"
                                                                         Value="@drugName"
                                                                         ValueChanged="OnDrugNameChanged"
                                                                         SearchFunc="SearchBrandNames"
                                                                         ToStringFunc="@(s => s)"
                                                                         CoerceText="true"
                                                                         Clearable="true"
                                                                         Dense="true"
                                                                         ResetValueOnEmptyText="true"
                                                                         Margin="Margin.Dense"
                                                                         Variant="Variant.Outlined"
                                                                         MinCharacters="3"
                                                                         Style="width: 100%;" />
                                                    </MudItem>
                                                    <MudItem xs="4">
                                                        <MudSelect T="string"
                                                                   Label="@Localizer["Dosage & InTake"]"
                                                                   @bind-Value="finalSBD"
                                                                   Dense="true"
                                                                   Margin="Margin.Dense"
                                                                   Variant="Variant.Outlined"
                                                                   Style="width: 100%;">
                                                            @foreach (var drugs in BrandSBD)
                                                            {
                                                                <MudSelectItem T="string" Value="@drugs">@drugs</MudSelectItem>
                                                            }
                                                        </MudSelect>
                                                    </MudItem>
                                                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                                                        <MudButton Color="Color.Primary"
                                                                   OnClick="AddNewMedication"
                                                                   Variant="Variant.Filled"
                                                                   Dense="true"
                                                                   Style="min-width: 100px; height: 40px;">
                                                            @Localizer["Add"]
                                                        </MudButton>
                                                    </MudItem>
                                                </MudGrid>
                                                <SfGrid @ref="MedicinesGrid"
                                                        TValue="OrderSetActiveMedication"
                                                        Style="font-size: 0.85rem; margin-top: 24px;"
                                                        DataSource="@medications"
                                                        AllowPaging="true">
                                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                    <GridPageSettings PageSize="5"></GridPageSettings>
                                                    <GridEvents TValue="OrderSetActiveMedication"></GridEvents>
                                                    <GridColumns>
                                                        <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                        <GridColumn Field="BrandName" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                                                        <GridColumn Field="DrugDetails" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200"></GridColumn>
                                                        <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1"></GridColumn>
                                                        <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                                                        <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>
                                                        <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>

                                                        <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)"
                                                                    HeaderText="@Localizer["Chief Complaint"]"
                                                                    TextAlign="TextAlign.Center"
                                                                    Width="200"
                                                                    EditTemplate="@ChiefComplaintEditTemplate">
                                                        </GridColumn>


                                                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                            <GridCommandColumns>
                                                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                            </GridCommandColumns>
                                                        </GridColumn>
                                                    </GridColumns>
                                                </SfGrid>
                                            }
                                           
                                             
                                            <div class="button-container">
                                            <MudButton Variant="Variant.Filled" OnClick="ToggleImmunizationForm" Color="Color.Primary" Class="toggle-button" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                    @Localizer["Immunization Details"]
                                                </MudButton>
                                            </div>
                                             @if (showImmunizationForm)
                                            {
                                                <MudGrid>
                                                <MudItem xs="8" Style="padding-left: 24px; margin-top: 5px">
                                                   
                                                    <MudText Typo="Typo.h6" Style="margin-bottom: 16px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Search Immunization"]</MudText>
                                                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                                                            <MudAutocomplete T="string"
                                                                             Label="@Localizer["Search Vaccines"]"
                                                                             Value="VaccineName"
                                                                             ValueChanged="OnICDNameChanged"
                                                                             SearchFunc="SearchVaccinesData"
                                                                             ToStringFunc="@(s => s)"
                                                                             CoerceText="true"
                                                                             Clearable="true"
                                                                             Dense="true"
                                                                             ResetValueOnEmptyText="true"
                                                                             Variant="Variant.Outlined"
                                                                             Margin="Margin.Dense"
                                                                             MinCharacters="1"
                                                                             Style="flex-grow: 1;" />

                                                            <MudButton Color="Color.Primary"
                                                                       OnClick="AddNewSurgery"
                                                                       Variant="Variant.Filled"
                                                                       Style="height: 35px; min-width: 85px;">
                                                                @Localizer["Add"]
                                                            </MudButton>
                                                        </div>
                                                        </MudItem>
                                                <MudItem xs="12" Style="padding-left: 24px;">
                                                    
                                                    <MudText Typo="Typo.h6" Style="margin-bottom: 16px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                            @Localizer["Vaccine Details"]
                                                        </MudText>
                                                        <MudGrid>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["Vaccine Name"]</MudText>
                                                                <MudTextField @bind-Value="VaccineName" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                                                            </MudItem>

                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["CPT Code"]</MudText>
                                                                <MudTextField @bind-Value="SelectedCPTCode" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                                                            </MudItem>

                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["CVX Code"]</MudText>
                                                                <MudTextField @bind-Value="SelectedCVXCode" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                                                            </MudItem>

                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["CPT Description"]</MudText>
                                                                <MudTextField @bind-Value="SelectedCPTDescription" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                                                            </MudItem>

                                                            <MudItem xs="4" >
                                                                <MudText Typo="Typo.body1">@Localizer["Comments"]</MudText>
                                                                <MudTextField @bind-Value="Comments" Lines="1" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                                                            </MudItem>
                                                        </MudGrid>
                                                    </MudItem>

                                                    <MudItem xs="12" Style="padding-right: 32px; border-right: 1px solid #E0E0E0;">
                                                        <SfGrid @ref="ImmunizationGrid" TValue="OrderSetImmunizationData" Style="font-size: 0.85rem; margin-top: 24px; width: 100%;"
                                                                DataSource="@immunization" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="10"></GridPageSettings>
                                                            <GridEvents TValue="OrderSetImmunizationData"></GridEvents>
                                                            <GridColumns>
                                                                <GridColumn Field="ImmunizationId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="GivenDate" HeaderText="@Localizer["Date"]" Width="100" Format="MM/dd/y" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                                <GridColumn Field="Immunizations" HeaderText="@Localizer["Vaccine"]" Width="200" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="CVXCode" HeaderText="@Localizer["CVX Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Comments" HeaderText="@Localizer["Comments"]" Width="180" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                                <GridColumn HeaderText="@Localizer["Actions"]" Width="80" TextAlign="TextAlign.Center">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudItem>
                                                </MudGrid>
                                            }
                                            <div class="button-container">
                                            <MudButton Variant="Variant.Filled" OnClick="ToggleProcedureForm" Color="Color.Primary" Class="toggle-button" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                    @Localizer["Procedure Details"]
                                                </MudButton>
                                            </div>
                                             @if (showProcedureForm)
                                            {
    
                                                <MudGrid>
                                                    <MudItem xs="4">

                                                        <MudAutocomplete T="CPT"
                                                                         Label="@Localizer["Search CPT By Codes or Description"]"
                                                                         Value="@selectedCPT"
                                                                         ValueChanged="OnCPTSelected"
                                                                         SearchFunc="SearchCPTCodes"
                                                                     ToStringFunc="@(cpt =>
                                                                cpt != null && !string.IsNullOrWhiteSpace(cpt.CPTCode + cpt.Description)
                                                                    ? $"{cpt.CPTCode}{(string.IsNullOrWhiteSpace(cpt.Description) ? "" : $" - {cpt.Description}")}"
                                                                    : "")"
                                                                         CoerceText="true"
                                                                         Clearable="true"
                                                                         ResetValueOnEmptyText="true"
                                                                         Variant="Variant.Outlined"
                                                                         Margin="Margin.Dense"
                                                                         MinCharacters="2"
                                                                         Style="width: 100%;" />
                                                    </MudItem>

                                                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                                                        <MudButton Color="Color.Primary"
                                                                   OnClick="AddNewProcedure"
                                                                   Variant="Variant.Filled"
                                                                   Dense="true"
                                                                   Style="min-width: 70px; height: 35px;">
                                                            @Localizer["Add"]
                                                        </MudButton>
                                                    </MudItem>
                                                

                                                    <MudItem xs="12" Style="padding-right: 32px; border-right: 1px solid #E0E0E0;">
                                                        <SfGrid @ref="ProcedureGrid" TValue="OrderSetProcedures" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@procedure" AllowPaging="true" AllowEditing="true">
                                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                    <GridPageSettings PageSize="10"></GridPageSettings>
                                                    <GridEvents  TValue="OrderSetProcedures"></GridEvents>
                                                    <GridColumns>
                                                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                        <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                        <GridColumn Field="Description" HeaderText="@Localizer["Descriptions"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                        <GridColumn Field="@nameof(Procedures.AssessmentData)"
                                                                    HeaderText="@Localizer["RelatedAssessment"]"
                                                                    TextAlign="TextAlign.Center"
                                                                    Width="150"
                                                                    EditTemplate="@AssessmentEditTemplate">
                                                        </GridColumn>
                                                        <GridColumn Field="OrderedBy" HeaderText="@Localizer["Ordered By"]" TextAlign="TextAlign.Center" Width="150" AllowEditing="false"></GridColumn>
                                                        <GridColumn Field="OrderDate" HeaderText="@Localizer["Order Date"]" Width="150" TextAlign="TextAlign.Center" Format="MM/dd/yy"></GridColumn>
                                                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                            <GridCommandColumns>
                                                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                            </GridCommandColumns>
                                                        </GridColumn>
                                                    </GridColumns>
                                                </SfGrid>
                                                    </MudItem>
                                                </MudGrid>
                                            }
                                            <div class="button-container" >
                                            <MudButton Variant="Variant.Filled" OnClick="ToggleShowLabsForm" Color="Color.Primary" Class="toggle-button" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                    @Localizer["Labs Details"]
                                                </MudButton>
                                            </div>
                                            @if (showLabsForm)
                                            {
                                                <MudGrid>
                                                    <MudItem xs="12" Style="padding-right: 32px; border-right: 1px solid #E0E0E0;">
                                                    <SfGrid @ref="PlanLabsGrid" TValue="OrderSetLabTests" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@planlabs" AllowPaging="true" Toolbar="@ToolbarItems">
                                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                    <GridPageSettings PageSize="5"></GridPageSettings>
                                                    <GridEvents TValue="OrderSetLabTests"></GridEvents>
                                                    <GridColumns>
                                                        <GridColumn Field="LabTestsId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="150" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                        <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" Width="150" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                            <GridColumn Field="LabTest1" HeaderText="@Localizer["Primary Test"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                                                         <GridColumn Field="LabTest2" HeaderText="@Localizer["Secondary Test"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn> 
                                                            <GridColumn Field="TestOrganization" HeaderText="@Localizer["Organization"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"
                                                                    EditTemplate="@OrganizationEditTemplate"></GridColumn>
                                                        <GridColumn Field="@nameof(LabTests.AssessmentData)"
                                                                    HeaderText="@Localizer["RelatedAssessment"]"
                                                                    TextAlign="TextAlign.Center"
                                                                    Width="150"
                                                                    EditTemplate="@AssessmentEditTemplate">
                                                        </GridColumn>
                                                        <GridColumn HeaderText="@Localizer["Actions"]" Width="100" TextAlign="TextAlign.Center">
                                                            <GridCommandColumns>
                                                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                                                            </GridCommandColumns>
                                                        </GridColumn>
                                                    </GridColumns>
                                                </SfGrid>
                                                    </MudItem>
                                                </MudGrid>

                                            }
                                        <div class="button-container">
                                            <MudButton Variant="Variant.Filled" OnClick="ToggleDiagnosticImagingForm" Color="Color.Primary" Class="toggle-button" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                @Localizer["Diagnostic Imaging Details"]
                                            </MudButton>
                                        </div>
                                        @if (showDiagnosticImagingForm)
                                        {
                                            <MudGrid Spacing="3" Class="mb-3 mt-1">
                                                <MudItem xs="6">
                                                    <MudPaper Class="pa-2">
                                                        <MudGrid Spacing="1">
                                                            <MudItem xs="12">
                                                                <MudText Typo="Typo.h6" Color="Color.Primary">@Localizer["Order Options"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="12">
                                                                <MudText Typo="Typo.body1"> </MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["DI Company"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["Type"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1"></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.DiCompany" /></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.Type" /></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1"></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["Lookup"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["Order name"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.body1">@Localizer["Starts with"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.Lookup" /></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.OrderName" /></MudText>
                                                            </MudItem>
                                                            <MudItem xs="4">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.StartsWith" /></MudText>
                                                            </MudItem>
                                                        </MudGrid>
                                                    </MudPaper>
                                                </MudItem>
                                                <MudItem xs="6">
                                                    <MudPaper Class="pa-3" Height="160px">
                                                        <MudGrid Spacing="2">
                                                            <MudItem xs="12">
                                                                <MudText Typo="Typo.body1">@Localizer["CC Results To"]</MudText>
                                                            </MudItem>
                                                            <MudItem xs="12">
                                                                <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.ccResults" /></MudText>
                                                            </MudItem>
                                                        </MudGrid>
                                                    </MudPaper>
                                                </MudItem>

                              
                                            </MudGrid>
                                        }
                                            <div class="button-container add-member-container" style="height: 60px;">
                                            <MudButton Variant="Variant.Filled" OnClick="HandleSubmit" Color="Color.Error" Class="submit-button mud-float-end mud-margin-top-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                        @Localizer["Add OrderSet"]
                                                    </MudButton>
                                            </div>
                                        }
                                        else if(showSelected)
                                        {
                                            <MudPaper Class="p-2 mb-2">
                                            <MudText Typo="Typo.h5" Color="Color.Primary" Class="mb-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Medications</MudText>

                                                <SfGrid @ref="MedicinesGrid"
                                                        TValue="OrderSetActiveMedication"
                                                        DataSource="@medications"
                                                        AllowPaging="true"
                                                        PageSettings-PageSize="5"
                                                    GridLines="GridLine.Both"
                                                        Style="font-size: 0.85rem;">
                                                    <GridEditSettings AllowAdding="true" AllowEditing="@updateOrderset" AllowDeleting="true" Mode="EditMode.Normal" />
                                                    <GridPageSettings PageSize="5" />
                                                    <GridColumns>
                                                        <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false" />
                                                    <GridColumn Field="BrandName" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120" />
                                                    <GridColumn Field="DrugDetails" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200" />
                                                        <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1" />
                                                        <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120" />
                                                        <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" Format="MM/dd/y" TextAlign="TextAlign.Center" Width="100" />
                                                        <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" Format="MM/dd/y" TextAlign="TextAlign.Center" Width="100" />
                                                        <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)" HeaderText="@Localizer["Chief Complaint"]" TextAlign="TextAlign.Center" Width="200" EditTemplate="@ChiefComplaintEditTemplate" />
                                                        @if(updateOrderset)
                                                        {
                                                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                            <GridCommandColumns>
                                                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                            </GridCommandColumns>
                                                        </GridColumn>
                                                        }
                                                    </GridColumns>
                                                </SfGrid>
                                            </MudPaper>

                                        
                                        <MudPaper Class="p-2 mb-2">
                                            <MudText Typo="Typo.h5" Color="Color.Primary" Class="mb-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Immunization Details</MudText>
                                            <SfGrid @ref="ImmunizationGrid" TValue="OrderSetImmunizationData" Style="font-size: 0.85rem;" DataSource="@immunization" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                                                        <GridEditSettings AllowAdding="true" AllowEditing="@updateOrderset" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                        <GridPageSettings PageSize="5"></GridPageSettings>
                                                        <GridEvents TValue="OrderSetImmunizationData"></GridEvents>
                                                        <GridColumns>
                                                            <GridColumn Field="ImmunizationId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                            <GridColumn Field="GivenDate" HeaderText="@Localizer["Date"]" Width="100" Format="MM/dd/y" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                            <GridColumn Field="Immunizations" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Vaccine"]" Width="200" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="CVXCode" HeaderText="@Localizer["CVX Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="Comments" HeaderText="@Localizer["Comments"]" Width="180" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                            @if (updateOrderset)
                                                            {
                                                                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            }
                                                        </GridColumns>
                                                    </SfGrid>
                                        </MudPaper>

                                        <MudPaper Class="p-2 mb-2">
                                            <MudText Typo="Typo.h5"  Color="Color.Primary" Class="mb-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Procedure Details"]</MudText>
                                            <SfGrid @ref="ProcedureGrid" TValue="OrderSetProcedures" Style="font-size: 0.85rem;" DataSource="@procedure" AllowPaging="true" AllowEditing="true" GridLines="GridLine.Both">
                                                        <GridEditSettings AllowAdding="true" AllowEditing="@updateOrderset" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                        <GridPageSettings PageSize="5"></GridPageSettings>
                                                        <GridEvents TValue="OrderSetProcedures"></GridEvents>
                                                        <GridColumns>
                                                            <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                            <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="Description" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Descriptions"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="Notes" ClipMode="ClipMode.EllipsisWithTooltip"  HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                            <GridColumn Field="@nameof(Procedures.AssessmentData)"
                                                                        HeaderText="@Localizer["RelatedAssessment"]"
                                                                        TextAlign="TextAlign.Center"
                                                                        Width="150"
                                                                        EditTemplate="@AssessmentEditTemplate">
                                                            </GridColumn>
                                                            <GridColumn Field="OrderedBy" HeaderText="@Localizer["Ordered By"]" TextAlign="TextAlign.Center" Width="150" AllowEditing="false"></GridColumn>
                                                            <GridColumn Field="OrderDate" HeaderText="@Localizer["Order Date"]" Width="150" TextAlign="TextAlign.Center" Format="MM/dd/yy"></GridColumn>
                                                            @if (updateOrderset)
                                                            {
                                                                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            }
                                                        </GridColumns>
                                                    </SfGrid>
                                        </MudPaper>
                                        <MudPaper Class="p-2 mb-2">
                                            <MudText Typo="Typo.h5" Color="Color.Primary" Class="mb-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Labs Details"]</MudText>


                                            <SfGrid @ref="PlanLabsGrid" Style="font-size: 0.85rem;" TValue="OrderSetLabTests" DataSource="@planlabs" AllowPaging="true" GridLines="GridLine.Both">
                                                        <GridEditSettings AllowAdding="true" AllowEditing="@updateOrderset" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                        <GridPageSettings PageSize="5"></GridPageSettings>
                                                        <GridEvents TValue="OrderSetLabTests"></GridEvents>
                                                        <GridColumns>
                                                            <GridColumn Field="LabTestsId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                            <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="150" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                            <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" Width="150" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                            <GridColumn Field="LabTest1" HeaderText="@Localizer["Primary Test"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="LabTest2" HeaderText="@Localizer["Secondary Test"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="TestOrganization" HeaderText="@Localizer["Organization"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"
                                                                        EditTemplate="@OrganizationEditTemplate"></GridColumn>
                                                            <GridColumn Field="@nameof(LabTests.AssessmentData)"
                                                                        HeaderText="@Localizer["RelatedAssessment"]"
                                                                        TextAlign="TextAlign.Center"
                                                                        Width="150"
                                                                        EditTemplate="@AssessmentEditTemplate">
                                                            </GridColumn>
                                                            @if (updateOrderset)
                                                            {
                                                                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            }
                                                        </GridColumns>
                                                    </SfGrid>
                                        </MudPaper>

                                        <MudPaper Class="p-2 mb-2">
                                            <MudText Typo="Typo.h5" Color="Color.Primary" Class="mb-2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Diagnostic Imaging Details"]</MudText>

                                            <SfGrid @ref="futureImagingGrid" TValue="OrderSetDiagnosticImaging" Style="font-size: 0.85rem;" DataSource="@DiagnosticImagingList" AllowPaging="true" GridLines="GridLine.Both">
                                                        <GridEditSettings AllowEditing="@updateOrderset" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                                                        <GridPageSettings PageSize="5"></GridPageSettings>
                                                        <GridColumns>
                                                            <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                            <GridColumn Field="DiCompany" HeaderText="DI Company" Width="150" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="Type" HeaderText="Type" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="Lookup" HeaderText="Lookup" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="OrderName" HeaderText="Order Name" Width="200" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="StartsWith" HeaderText="Starts With" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                            <GridColumn Field="ccResults" HeaderText="CC Results To" Width="200" TextAlign="TextAlign.Left"></GridColumn>
                                                            @if (updateOrderset)
                                                            {
                                                                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            }
                                                        </GridColumns>
                                                   </SfGrid>
                                        </MudPaper>

                                        @if(updateOrderset){
                                            <div class="button-container add-member-container" style="height: 60px;">
                                                <MudButton Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Filled" OnClick="HandleUpdateOrderSet" Color="Color.Error" Class="submit-button mud-float-end mud-margin-top-2">
                                                    @Localizer["Update OrderSet"]
                                                </MudButton>
                                            </div>
                                        }
                                        else if(deleteOrderset){
                                            <div class="button-container add-member-container" style="height: 60px;">
                                                <MudButton Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Filled" OnClick="HandleDeleteOrderSet" Color="Color.Error" Class="submit-button mud-float-end mud-margin-top-2">
                                                    @Localizer["Delete OrderSet"]
                                                </MudButton>
                                            </div>
                                        }
                                        }
                                    </MudItem>
                                </MudGrid>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>

        </MudGrid>
    </MudContainer>
</div>

<style>
    .button-spacing {
        margin-right: 20px;
    }

    .mud-container-center {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 90%;
        margin: auto;
        padding-top: 75px;
    }

    .button-container {
        width: 100%;
        margin-top: 1rem;
        display: flex;
        height:30px;
        justify-content: center;
    }

    .custom-datepicker .mud-input {
        width: 230px;
    }

    .toggle-button {
        width: 100%;
        max-width: 100%;
    }

    .full-width-paper {
        width: 100%;
    }

    .add-member-container {
        margin-top: 25px;
        padding-top: 25px;
        display: flex;
        justify-content: flex-end;
        width: 100%;
        margin-top: 1rem;
    }

    .mud-input-label {
        color: #333 !important;
    }

    .add-member-button {
        width: auto;
        margin: 0;
    }

    .mud-textfield {
        margin-bottom: 1rem;
    }

    .button-spacing {
        margin-right: 20px;
    }

    .flex-container {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
    }

        .flex-container > * {
            flex: 1;
            max-width: 33%;
            box-sizing: border-box;
        }

    .custom-form-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .custom-form-row {
        display: flex;
        gap: 1rem;
    }

        .custom-form-row > * {
            flex: 1;
            max-width: 33%;
        }

</style>






﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Syncfusion.Blazor.Buttons;
using Unity;
using Syncfusion.Blazor.Navigations;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using System.Text.RegularExpressions;
using System.Text;

namespace TeyaWebApp.Components.Pages
{
    public partial class Procedure
    {
        [Inject] private ICustomProcedureAlertService CustomProcedureAlertService { get; set; }
        [Inject] public IAlertService AlertService { get; set; }
        [Inject] public ICPTService _CPTService { get; set; }
        [Inject] private ILogger<Procedures> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }


        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; } // Added ChiefComplaintService
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] public SharedNotesService SharedNotesService { get; set; }
        private SfRichTextEditor richTextEditor;

        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private MudDialog showBrowsePopup { get; set; }
        private SfGrid<Procedures> ProcedureGrid;
        private Patient _PatientData = new Patient();
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        private CPT selectedCPT;
        private Procedures selectedProcedure;
        private List<string> AssessmentDiagnosis = new List<string>();
        private List<string> chiefComplaints = new List<string>(); // Added for chief complaints
        private List<ChiefComplaintDTO> chiefComplaintData = new List<ChiefComplaintDTO>(); // Added for chief complaint data
        public string CPTName { get; set; }
        [Inject] IAssessmentsService assessmentsService { get; set; }
        private List<Procedures> procedure = new();
        private List<Procedures> addedProcedure = new();
        private List<Procedures> updatedProcedure = new();
        private List<Procedures> deletedProcedure = new();
        private List<AssessmentsData> Localdata = new();
        private List<CPT> _cptCodes { get; set; } = new List<CPT>();

        private bool add = false;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };
        protected override async Task OnInitializedAsync()
        {
            try
            {
                _PatientData = PatientService.PatientData;
                PatientId = PatientService.PatientData.Id;
                OrgID = PatientService.PatientData.OrganizationID;
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                await LoadProcedureAsync();
                Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
                await LoadChiefComplaintDataAsync();

                SharedNotesService.OnChange += UpdateAssessments;
                AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
                _cptCodes = await _CPTService.GetAllCPTCodesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }
        private async Task LoadChiefComplaintDataAsync()
        {
            try
            {
                chiefComplaintData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription)).ToList();
                chiefComplaints = chiefComplaintData.Select(c => c.Description).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading chief complaint data");
            }
        }

        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();
        }

        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", process.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        process.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(process.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task<IEnumerable<CPT>> SearchCPTCodes(string value, CancellationToken cancellationToken)
        {
            return _cptCodes.Where(cpt =>
                (cpt.CPTCode?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (cpt.Description?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }

        private async void AddNewProcedure()
        {
            if (selectedCPT == null)
            {
                Snackbar.Add(Localizer["Please select a CPT code first"], Severity.Warning);
                return;
            }

            var newProcedure = new Procedures
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                OrganizationId = OrgID ?? Guid.Empty,
                PcpId = Guid.Parse(User.id),
                CPTCode = selectedCPT.CPTCode,
                OrderedBy = User.givenName + " " + User.surname,
                Description = selectedCPT.Description,
                Notes = string.Empty,
                OrderDate = DateTime.Now,
                CreatedByUserId = Guid.Parse(User.id),
                UpdatedByUserId = Guid.Parse(User.id),
                LastUpdatedDate = DateTime.Now,
                IsDeleted = true,
            };

            procedure.Add(newProcedure);
            addedProcedure.Add(newProcedure);
            ResetInputFields();
            await ProcedureGrid.Refresh();
        }

        public async Task ActionBeginHandler(ActionEventArgs<Procedures> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedProcedure.Add(args.Data);
                args.Data.IsDeleted = false;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedProcedure.Contains(args.Data) && !updatedProcedure.Contains(args.Data))
                {
                    args.Data.UpdatedByUserId = Guid.Parse(User.id);
                    args.Data.LastUpdatedDate = DateTime.Now;
                    updatedProcedure.Add(args.Data);
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaints);
            builder.AddAttribute(2, "Value", process.ChiefComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.ChiefComplaint = value;
                    var selectedComplaint = chiefComplaintData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        process.ChiefComplaintId = selectedComplaint.Id;
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        private async Task SaveChanges()
        {
            var alertsCheckList = new List<Procedures>(addedProcedure);
            try
            {
                if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }
                if (updatedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }

                if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.ChiefComplaint)))
                {
                    Snackbar.Add(Localizer["Chief Complaint is Blank"], Severity.Warning);
                    return;
                }

                var proceduresToCheck = new List<Procedures>();
                proceduresToCheck.AddRange(addedProcedure);
                proceduresToCheck.AddRange(updatedProcedure);


                if (addedProcedure.Any())
                {
                    await ProcedureService.AddProcedureAsync(addedProcedure, OrgID, Subscription);
                    addedProcedure.Clear();
                }

                if (updatedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(updatedProcedure, OrgID, Subscription);
                    updatedProcedure.Clear();
                }

                if (deletedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(deletedProcedure, OrgID, Subscription);
                    deletedProcedure.Clear();
                }

                await LoadProcedureAsync();
                richTextContent = GenerateRichTextContent();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                await CheckProceduresForSafetyAlerts(alertsCheckList);
                Snackbar.Add(Localizer["Changes saved successfully!"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        private async Task CancelChanges()
        {
            addedProcedure.Clear();
            updatedProcedure.Clear();
            deletedProcedure.Clear();
            await LoadProcedureAsync();
            CloseBrowsePopup();
            Snackbar.Add("Changes discarded", Severity.Info);
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task LoadProcedureAsync()
        {
            try
            {
                procedure = await ProcedureService.LoadProcedureAsync(PatientId, OrgID, Subscription);

                foreach (var proc in procedure)
                {
                    if (!string.IsNullOrEmpty(proc.ChiefComplaintId?.ToString()) && string.IsNullOrEmpty(proc.ChiefComplaint))
                    {
                        var complaint = chiefComplaintData.FirstOrDefault(c => c.Id == proc.ChiefComplaintId);
                        if (complaint != null)
                        {
                            proc.ChiefComplaint = complaint.Description;
                        }
                    }
                }

                richTextContent = GenerateRichTextContent();

                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();
                if (ProcedureGrid != null)
                    await ProcedureGrid.Refresh();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading Procedure");
            }
        }

        private string GenerateRichTextContent() =>
     "<table border='1' style='border-collapse: collapse; width: 100%; table-layout: fixed; font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; line-height: 1.2;'>" +
     "<tr>" +
         "<th style='width: 12%; word-wrap: break-word;'>Order Date</th>" +
         "<th style='width: 10%; word-wrap: break-word;'>CPT</th>" +
         "<th style='width: 18%; word-wrap: break-word;'>Description</th>" +
         "<th style='width: 15%; word-wrap: break-word;'>Notes</th>" +
         "<th style='width: 15%; word-wrap: break-word;'>Assessment</th>" +
         "<th style='width: 15%; word-wrap: break-word;'>Chief Complaint</th>" + // Added Chief Complaint column
         "<th style='width: 15%; word-wrap: break-word;'>Ordered By</th>" +
     "</tr>" +
     string.Join("", procedure.OrderByDescending(o => o.OrderDate)
         .Select(o => $"<tr>" +
             $"<td style='word-wrap: break-word;'>{o.OrderDate:yyyy-MM-dd}</td>" +
             $"<td style='word-wrap: break-word;'>{o.CPTCode}</td>" +
             $"<td style='word-wrap: break-word;'>{o.Description}</td>" +
             $"<td style='word-wrap: break-word;'>{o.Notes}</td>" +
             $"<td style='word-wrap: break-word;'>{o.AssessmentData}</td>" +
             $"<td style='word-wrap: break-word;'>{o.ChiefComplaint}</td>" + // Added Chief Complaint data
             $"<td style='word-wrap: break-word;'>{o.OrderedBy}</td>" +
         $"</tr>")) +
     "</table>";

        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup.CloseAsync();
        }

        private async Task OnCPTSelected(CPT selected)
        {
            selectedCPT = selected;
            StateHasChanged();
        }

        private void ResetInputFields()
        {
            selectedCPT = null;
        }

        private async Task OpenBrowsePopup()
        {
            if (showBrowsePopup != null)
            {
                await showBrowsePopup.ShowAsync();
            }
            else
            {
                Logger.LogError("showBrowsePopup is null");
            }
        }

        /// <summary>
        /// Checks procedures for potential safety concerns based on chief complaint and procedure description
        /// </summary>
        /// <param name="proceduresToCheck">List of procedures to evaluate</param>
        /// <returns>Boolean indicating whether any safety alerts were generated</returns>
        private async Task<bool> CheckProceduresForSafetyAlerts(List<Procedures> proceduresToCheck)
        {
            if (proceduresToCheck == null || !proceduresToCheck.Any())
                return false;

            int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();
            HashSet<string> checkedProcedureCombinations = new HashSet<string>();

            var customProcedureAlerts = await GetCustomProcedureAlertsForProcedures(proceduresToCheck);

            if (customProcedureAlerts.Count > 0)
            {
                foreach (var customAlert in customProcedureAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = _PatientData.Id,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = _PatientData.OrganizationID ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured Procedure Alert",
                        Description = customAlert.Description ?? $"Custom alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}. See reference: {customAlert.WebReference}",
                        AdditionalInfo = $"Order Set: {customAlert.OrderSet}, Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    Snackbar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                Severity.Info,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.MedicalServices;
                                });
                }
            }

            foreach (var procedure in proceduresToCheck)
            {
                if (string.IsNullOrEmpty(procedure.ChiefComplaint) || string.IsNullOrEmpty(procedure.Description))
                    continue;

                string combinationKey = $"{procedure.Description}|{procedure.ChiefComplaint}";
                if (checkedProcedureCombinations.Contains(combinationKey))
                    continue;
                checkedProcedureCombinations.Add(combinationKey);

                var chiefComplaint = chiefComplaintData.FirstOrDefault(c => c.Id == procedure.ChiefComplaintId);
                if (chiefComplaint == null)
                    continue;

                bool isAppropriate = await CheckProcedureMatchesChiefComplaint(procedure.Description, procedure.ChiefComplaint);

                if (!isAppropriate)
                {
                    string safetyResponse = await GetProcedureSafetyEvaluation(procedure.Description, procedure.ChiefComplaint);

                    string severityLevel = ExtractSeverityLevel(safetyResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = _PatientData.Id,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = _PatientData.OrganizationID ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested Alert",
                        Description = $"The procedure '{procedure.Description}' may not be appropriate for chief complaint '{procedure.ChiefComplaint}'.",
                        Solution = $"Consider reviewing the procedure order or consulting with a specialist. {safetyResponse}",
                        AdditionalInfo = $"Procedure: {procedure.Description}, Chief Complaint: {procedure.ChiefComplaint}, Patient Age: {patientAge}, Gender: {patientGender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    Snackbar.Add($"[AI SUGGESTED ALERT] ({patientAge}y, {patientGender}): {procedure.Description} may not be appropriate for {procedure.ChiefComplaint}. Severity: {severityLevel}",
                                snackbarSeverity,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.SmartToy;
                                });
                }
            }

            if (alertsToAdd.Count > 0)
            {
                try
                {
                    await AlertService.AddAlertsAsync(alertsToAdd, _PatientData.OrganizationID, false);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorAddingAlerts"]);
                }
            }

            return alertsToAdd.Count > 0;
        }

        private async Task<bool> CheckProcedureMatchesChiefComplaint(string procedureName, string chiefComplaint)
        {
            // Include patient data in the prompt
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string prompt = $"Does the procedure '{procedureName}' match with the chief complaint '{chiefComplaint}' for a {age}-year-old {gender} patient? Answer only with 'yes' or 'no'.";

            string response = await AskGptModel(prompt);

            return response.Trim().ToLower().Contains("yes", StringComparison.OrdinalIgnoreCase);
        }

        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }
        /// Gets a complete safety evaluation for a procedure with a specific chief complaint using the GPT model
        /// </summary>
        /// <param name="procedureDescription">Description of the procedure</param>
        /// <param name="chiefComplaint">Chief complaint</param>
        /// <returns>Safety alert with severity level and explanation in 2 lines</returns>
        private async Task<string> GetProcedureSafetyEvaluation(string procedureDescription, string chiefComplaint)
        {
            try
            {
                // Updated prompt to clarify that unrelated procedures are usually safe
                // We now specifically ask about negative interactions or contraindications
                string prompt = $"Evaluate if procedure '{procedureDescription}' could negatively affect or is contraindicated for a patient with chief complaint '{chiefComplaint}'. If there is no contraindication or negative interaction expected (even if the procedure is unrelated to treating the complaint), respond with 'Safe to proceed'. If there could be a negative effect or contraindication, provide: 1) Severity Level (Low/Medium/High) and 2) Brief reason in two sentences why it may be unsafe. Format as 'Reason: [your reason]. Severity Level: [level]'";

                string systemMessage = "You are a concise medical assistant evaluating procedure safety. Only flag procedures that could worsen a patient's condition or have a contraindication with their chief complaint. Procedures unrelated to the chief complaint are typically safe unless they specifically might harm the patient given their complaint. Keep responses brief and focused only on direct safety concerns or contraindications.";

                return await MeasureService.AskGptAsync(systemMessage, prompt);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error calling GPT model");
                return "Unable to evaluate safety - please review procedure manually.";
            }
        }

        private async Task<List<CustomProcedureAlerts>> GetCustomProcedureAlertsForProcedures(List<Procedures> proceduresToCheck)
        {
            try
            {
                var customProcedureAlerts = await CustomProcedureAlertService.GetAllByIdAndIsActiveAsync(_PatientData.Id, _PatientData.OrganizationID, false);
                if (customProcedureAlerts == null || customProcedureAlerts.Count == 0)
                    return new List<CustomProcedureAlerts>();

                int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<CustomProcedureAlerts>();

                var procedureDetails = new List<(string ProcedureName, string ChiefComplaint)>();
                foreach (var procedure in proceduresToCheck)
                {
                    if (string.IsNullOrEmpty(procedure.ChiefComplaint) || string.IsNullOrEmpty(procedure.Description))
                        continue;

                    var chiefComplaint = chiefComplaintData.FirstOrDefault(c => c.Id == procedure.ChiefComplaintId);
                    if (chiefComplaint == null)
                        continue;

                    procedureDetails.Add((procedure.Description, procedure.ChiefComplaint));
                }

                if (procedureDetails.Count == 0)
                    return matchingAlerts;

                var procedureNames = procedureDetails.Select(p => p.ProcedureName).ToList();

                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customProcedureAlerts.Count; i++)
                {
                    var alert = customProcedureAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                StringBuilder procedureComplaintInfo = new StringBuilder();
                foreach (var detail in procedureDetails)
                {
                    procedureComplaintInfo.AppendLine($"- {detail.ProcedureName} for {detail.ChiefComplaint}");
                }

                StringBuilder alertsSummary = new StringBuilder();
                for (int i = 0; i < customProcedureAlerts.Count; i++)
                {
                    var alert = customProcedureAlerts[i];
                    alertsSummary.AppendLine($"Alert {i + 1}: {alert.Name} - {alert.Description}");
                }

                string prompt = $"Determine which alerts apply to these procedure-complaint pairs:\n\n" +
                    $"Patient: {patientAge}y, {patientGender}\n" +
                    $"Procedure-Complaint Pairs:\n{procedureComplaintInfo}\n\n" +
                    $"Alerts:\n{alertsSummary}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";

                // Ask GPT
                string response = await AskGptModel(prompt);

                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customProcedureAlerts.Count)
                        {
                            matchingAlerts.Add(customProcedureAlerts[alertIndex - 1]);
                        }
                    }
                }

                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customProcedureAlerts)
                    {
                        bool procedureMatch = false;

                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.Name.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.Description.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var procedureName in procedureNames)
                            {
                                if (alert.OrderSet.Contains(procedureName, StringComparison.OrdinalIgnoreCase))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!procedureMatch)
                        {
                            var procedureKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                            foreach (var procedure in procedureNames)
                            {
                                var words = procedure.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        procedureKeywords.Add(word);
                                }
                            }

                            var alertKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                            if (!string.IsNullOrEmpty(alert.Name))
                            {
                                var words = alert.Name.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.Description))
                            {
                                var words = alert.Description.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        alertKeywords.Add(word);
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.OrderSet))
                            {
                                var words = alert.OrderSet.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            foreach (var procedureKeyword in procedureKeywords)
                            {
                                if (alertKeywords.Contains(procedureKeyword))
                                {
                                    procedureMatch = true;
                                    break;
                                }
                            }
                        }

                        if (procedureMatch)
                        {
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                return new List<CustomProcedureAlerts>();
            }
        }
        private async Task<string> AskGptModel(string prompt)
        {
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string systemMessage = $"You are a medical assistant helping to evaluate the appropriateness and safety of medical procedures for specific chief complaints. " +
                                   $"The patient is {age} years old and {gender}. " +
                                   $"Consider age-appropriate and gender-specific medical considerations in your evaluation. " +
                                   $"Take into account procedural risks, contraindications, and potential complications based on the patient's demographics. " +
                                   $"Provide concise, accurate information that prioritizes patient safety and clinical appropriateness.";

            return await MeasureService.AskGptAsync(systemMessage, prompt);
        }
    }
}
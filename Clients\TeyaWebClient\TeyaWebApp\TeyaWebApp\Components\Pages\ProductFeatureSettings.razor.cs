using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaWebApp.Services;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using Microsoft.Extensions.Logging;

namespace TeyaWebApp.Components.Pages
{
    public partial class ProductFeatureSettings
    {
        [Inject] private IProductFeatureService ProductFeatureService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        [Inject] private ILogger<ProductFeatureSettings> Logger { get; set; }

        private List<Product> ProductList { get; set; } = new();
        private ProductRegistrationDto newProduct { get; set; } = new();
        private List<ProductRegistrationDto> NewProductRegistrationList { get; set; } = new();
        private List<ProductFeature> AllProductFeatures { get; set; } = new();
        private List<ProductFeature> FeaturesForSelectedProduct { get; set; } = new();
        private Product SelectedProduct { get; set; }
        private GridEditSettings ProductGridEditSettings { get; set; } = new() { AllowAdding = true, AllowEditing = true, AllowDeleting = true };
        private GridEditSettings FeatureGridEditSettings { get; set; } = new() { AllowAdding = true, AllowEditing = true, AllowDeleting = true };
        private SfGrid<Product> ProductGrid { get; set; }
        private SfGrid<ProductFeature> FeatureGrid { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Reset collections to avoid duplicates
                ProductList = new List<Product>();
                AllProductFeatures = new List<ProductFeature>();
                FeaturesForSelectedProduct = new List<ProductFeature>();
                SelectedProduct = null;

                // Fetch all products
                var products = await ProductService.GetProductsAsync();
                if (products != null)
                {
                    ProductList = products.ToList();
                }

                // Fetch all product features on initialization
                var features = await ProductFeatureService.GetAllProductFeaturesAsync();
                if (features != null)
                {
                    AllProductFeatures = features.ToList();
                }

                Logger.LogInformation("{PageName} page initialized successfully", nameof(ProductFeatureSettings));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing {PageName} page", nameof(ProductFeatureSettings));
            }
        }

        private async Task OnProductRowSelected(RowSelectEventArgs<Product> args)
        {
            await ShowProductFeatures(args.Data);
        }

        private async Task ShowProductFeatures(Product product)
        {
            if (product == null) return;

            try
            {
                SelectedProduct = product;

                // Filter features for the selected product
                FilterFeaturesForSelectedProduct();

                // Ensure UI updates
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error fetching features for product {product.Name}");
            }
        }

        private void FilterFeaturesForSelectedProduct()
        {
            if (SelectedProduct != null && AllProductFeatures != null)
            {
                FeaturesForSelectedProduct = AllProductFeatures
                    .Where(f => f.ProdId == SelectedProduct.Id)
                    .ToList();
            }
            else
            {
                FeaturesForSelectedProduct = new List<ProductFeature>();
            }
        }

        private async Task OnProductActionBegin(ActionEventArgs<Product> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    var product = args.Data;

                    if (product.Id == Guid.Empty)
                    {
                        // Adding new product
                        product.Id = Guid.NewGuid();

                        var newProductRegistration = new ProductRegistrationDto
                        {
                            Id = product.Id,
                            Name = product.Name,
                            Description = product.Description,
                            ByProduct = product.Byproduct
                        };

                        NewProductRegistrationList.Clear();
                        NewProductRegistrationList.Add(newProductRegistration);

                        await ProductService.RegisterProductsAsync(NewProductRegistrationList);
                        // Don't add to ProductList as the grid will handle this automatically
                        // after successful API call

                        Logger.LogInformation($"Added product '{product.Name}'");
                    }
                    else
                    {
                        // Updating existing product
                        await ProductService.UpdateProductAsync(product.Id, product);

                        // Don't update local list as the grid will handle this automatically

                        Logger.LogInformation($"Updated product '{product.Name}'");

                        // If this is the currently selected product, update it
                        if (SelectedProduct?.Id == product.Id)
                        {
                            SelectedProduct = product;
                            FilterFeaturesForSelectedProduct();
                        }
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    var product = args.Data;

                    await ProductService.DeleteProductByIdAsync(product.Id, product.OrganizationId, product.Subscription);

                    // Don't remove from local list as the grid will handle this automatically

                    // If this was the selected product, clear the selection
                    if (SelectedProduct?.Id == product.Id)
                    {
                        SelectedProduct = null;
                        FeaturesForSelectedProduct = new List<ProductFeature>();
                    }

                    Logger.LogInformation($"Deleted product '{product.Name}'");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing product grid action");
            }
        }

        private async Task OnProductActionComplete(ActionEventArgs<Product> args)
        {
            Logger.LogInformation($"Product grid action complete: {args.RequestType}");

            // If we've just added or edited a product, we need to refresh the product list
            // to avoid duplicates and ensure data consistency
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save ||
                args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                try
                {
                    // Refresh product list from service
                    var products = await ProductService.GetProductsAsync();
                    if (products != null)
                    {
                        ProductList = products.ToList();
                        StateHasChanged();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error refreshing product list after grid operation");
                }
            }
        }

        private async Task OnFeatureActionBegin(ActionEventArgs<ProductFeature> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    var feature = args.Data;

                    if (feature.Id == Guid.Empty)
                    {
                        // Adding new feature
                        feature.Id = Guid.NewGuid();
                        feature.ProdId = SelectedProduct.Id;
                        feature.ProdName = SelectedProduct.Name;
                        feature.Created = DateTime.Now;
                        feature.Updated = DateTime.Now;
                        feature.Status = true;

                        // Log before API call
                        Logger.LogInformation($"Adding new feature: ID={feature.Id}, Name={feature.FeatureName}");

                        await ProductFeatureService.AddProductFeatureAsync(feature);

                        // Add to the AllProductFeatures collection for filtering purposes
                        if (!AllProductFeatures.Any(f => f.Id == feature.Id))
                        {
                            AllProductFeatures.Add(feature);
                        }

                        Logger.LogInformation($"Added feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");
                    }
                    else
                    {
                        // Updating existing feature
                        feature.ProdId = SelectedProduct.Id;
                        feature.ProdName = SelectedProduct.Name;
                        feature.Updated = DateTime.Now;

                        // Log before API call
                        Logger.LogInformation($"Updating existing feature: ID={feature.Id}, Name={feature.FeatureName}");

                        await ProductFeatureService.UpdateProductFeatureAsync(feature);

                        // Update in AllProductFeatures list
                        var index = AllProductFeatures.FindIndex(f => f.Id == feature.Id);
                        if (index >= 0)
                        {
                            AllProductFeatures[index] = feature;
                        }

                        Logger.LogInformation($"Updated feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");
                    }

                    // Refresh the features list for the selected product
                    FilterFeaturesForSelectedProduct();
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    var feature = args.Data;

                    await ProductFeatureService.DeleteProductFeatureByIdAsync(feature.Id);

                    // Remove from AllProductFeatures list
                    AllProductFeatures.RemoveAll(f => f.Id == feature.Id);

                    Logger.LogInformation($"Deleted feature '{feature.FeatureName}' for product '{SelectedProduct.Name}'");

                    // Refresh the features list for the selected product
                    FilterFeaturesForSelectedProduct();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing feature grid action");
            }
        }

        private async Task OnFeatureActionComplete(ActionEventArgs<ProductFeature> args)
        {
            Logger.LogInformation($"Feature grid action complete: {args.RequestType}");

            // If we've just added, edited or deleted a feature, refresh the feature list
            // to avoid duplicates and ensure data consistency
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save ||
                args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                try
                {
                    // Refresh all features from the service
                    var features = await ProductFeatureService.GetAllProductFeaturesAsync();
                    if (features != null)
                    {
                        AllProductFeatures = features.ToList();

                        // Update the filtered list for the selected product
                        FilterFeaturesForSelectedProduct();
                        StateHasChanged();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error refreshing feature list after grid operation");
                }
            }
        }
    }
}
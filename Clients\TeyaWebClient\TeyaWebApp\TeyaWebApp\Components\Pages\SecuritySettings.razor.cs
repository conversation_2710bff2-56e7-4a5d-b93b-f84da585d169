using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaWebApp.TeyaAIScribeResources;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class SecuritySettings : ComponentBase
    {
        [Inject]
        private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; } 

        [Inject]
        private ILogger<SecuritySettings> Logger { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private List<Product>? Products { get; set; }
        private List<Member>? Members { get; set; }
        private Product? SelectedProduct { get; set; }
        private bool Subscription = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                Products = await ProductService.GetProductsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingProducts"]);
            }
        }

        private async Task OnRowSelected(RowSelectEventArgs<Product> args)
        {
            SelectedProduct = args.Data;

            try
            {
                Members = await MemberService.GetMembersForProductAsync(SelectedProduct.Id, SelectedProduct.OrganizationId, SelectedProduct.Subscription);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingMembersForProduct"], SelectedProduct.Id);
            }
        }

        private void UpdateMemberAccessLocally(Member member, object value)
        {
            member.IsActive = (bool)value;
        }

        private async Task SaveAllMemberAccess()
        {
            if (SelectedProduct != null && Members != null && Members.Any())
            {
                try
                {
                    foreach (var member in Members)
                    {
                        var memberAccessUpdate = new MemberAccessUpdate
                        {
                            MemberId = member.Id,
                            HasAccess = member.IsActive
                        };
                        var activeUserOrganizationId = member.OrganizationID ?? Guid.Empty;
                        var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                        var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                        Subscription = planType.PlanName == "Enterprise";
                        await ProductService.UpdateMembersAccessAsync(SelectedProduct.Id, memberAccessUpdate, activeUserOrganizationId, Subscription );
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorSavingMemberAccessUpdates"], SelectedProduct.Id);
                }
            }
        }
    }
}
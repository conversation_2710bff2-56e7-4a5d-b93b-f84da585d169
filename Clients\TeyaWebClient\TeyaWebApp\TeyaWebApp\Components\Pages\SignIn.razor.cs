﻿using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using TeyaUIViewModels.ViewModels;

namespace TeyaWebApp.Components.Pages
{
    public partial class SignIn : ComponentBase
    {
        private AuthenticationState authState;
        private bool isInitialized = false;
        private bool isLoading = true;
        private const int singleSeat = 1;
        private const int singleUser = 1;
        private const int zero = 0;
        private UserLicense UserLicense { get; set; } = new UserLicense();
        private List<PlanType> PlanTypes = new();
        [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Inject] private ILogger<SignIn> logger { get; set; }
        [Inject] private ActiveUser user { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private GraphApiService GraphApiService { get; set; }
        [Inject] private ITokenService TokenService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [Inject] private IPagePathService PagePathService { get; set; }
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; }
        private List<PageRoleMappingData> PageUrls = new();

        [Inject] private ILogger<License> Logger { get; set; }
        [Inject] private IRoleslistService _RoleslistService { get; set; }

        private List<String> roles;
        private Guid OrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IPredefinedVisitTypeService _PredefinedVisitTypeService { get; set; }
        [Inject] private IVisitTypeService _VisitTypeService { get; set; }

        private List<PredefinedVisitTypedata> predefinedvisittypes;
        private List<VisitType> visittypes;
        public enum UserRoles
        {
            Provider,
            Patient,
            Admin
        }
        public enum AppPages
        {
            OfficeVisit,
            Appointments
        }

        protected override async Task OnInitializedAsync()
        {
            if (!isInitialized)
            {
                isInitialized = true;
                authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();

                if (authState.User.Identity.IsAuthenticated)
                {
                    await ProcessLoggedInUser();
                    PlanTypes = (await PlanTypeService.GetAllPlanTypesAsync()).ToList();
                    var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                    try
                    {
                        var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);

                        var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                        Subscription = planType.PlanName == "Enterprise";
                        var activeMember = await MemberService.GetMemberByIdAsync(Guid.Parse(user.id),activeUserOrganizationId,Subscription);


                        if (await UserLicenseService.CheckLicenseExpiryAsync(activeUserOrganizationId))
                        {
                            await RedirectToUnlicensedAccess();
                            return;
                        }
                        else if (activeUserLicense.ActiveUsers <= activeUserLicense.Seats)
                        {
                            if (activeMember != null && activeMember.LicenseId == null)
                            {
                                activeMember.LicenseId = activeUserLicense.Id;
                                await MemberService.UpdateMemberByIdAsync(activeMember.Id, activeMember);
                                await UserLicenseService.IncrementActiveUsersAsync(activeUserOrganizationId);
                            }
                        }
                        else
                        {
                            logger.LogWarning("Maximum active users reached for organization: {OrganizationId}", activeUserOrganizationId);
                            await RedirectToUnlicensedAccess();
                            return;
                        }
                        if (user.role == "Provider")
                        {
                            NavigationManager.NavigateTo("/OfficeVisit");
                        }
                        else
                        {
                            NavigationManager.NavigateTo("/Appointments");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogInformation(ex, "Creating free license for new Organization");
                        await AddFreeUserLicense(activeUserOrganizationId);
                        if (user.role == "Provider")
                        {
                            NavigationManager.NavigateTo("/OfficeVisit");
                        }
                        else
                        {
                            NavigationManager.NavigateTo("/Appointments");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Adds a free user license for the specified organization.
        /// </summary>
        private async Task AddFreeUserLicense(Guid selectedOrganization)
        {
            if (selectedOrganization == Guid.Empty) return;

            try
            {
                var freePlanId = PlanTypes.FirstOrDefault(p => p.PlanName == "Free")?.Id ?? Guid.Empty;

                var freeUserLicense = new UserLicense
                {
                    Id = Guid.NewGuid(),
                    PlanId = freePlanId,
                    OrganizationId = selectedOrganization,
                    Seats = singleSeat,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(user.id),
                    Status = true,
                    ExpiryDate = DateTime.UtcNow.AddDays(14),
                    ActiveUsers = singleUser
                };

                await UserLicenseService.AddUserLicenseAsync(freeUserLicense);

                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);

                var activeMember = await MemberService.GetMemberByIdAsync(Guid.Parse(user.id),activeUserOrganizationId,Subscription);
                if (activeMember != null)
                {
                    activeMember.LicenseId = freeUserLicense.Id;
                    await MemberService.UpdateMemberByIdAsync(activeMember.Id, activeMember);
                }
                Logger.LogInformation("Free user license added for organization {OrganizationId}", selectedOrganization);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding free user license");
            }
        }

        private async Task RedirectToUnlicensedAccess()
        {
            var unlicensed = Localizer["UnlicensedAccess"].Value;
            NavigationManager.NavigateTo(unlicensed, forceLoad: true);
        }

        private async Task ProcessLoggedInUser()
        {
            try
            {
                await AuthService.GetLoggedInUserDetailsAsync();
                var result = await AuthService.GetUserDetailsAsync();

                if (result)
                {
                    var userDetails = JsonSerializer.Deserialize<Dictionary<string, object>>(AuthService.UserDetails);
                    TokenService.UserDetails = AuthService.UserDetails;
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName); 
                    bool userExists = await MemberService.SearchMembersEmailAsync(user.mail,OrganizationId, Subscription);
                    if (!userExists)
                    {
                        var newMember = await CreateMemberFromUserDetails();
                        var responseMessage = await MemberService.RegisterMembersContentAsync(new List<Member> { newMember });

                        if (responseMessage != null)
                        {
                            logger.LogInformation(Localizer["MemberRegisteredSuccess"]);
                        }
                    }
                }
                else
                {
                    AuthService.UserDetails = Localizer["UserDetailsFetchFailed"];
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, Localizer["ErrorFetchingOrRegistering"]);
            }
        }
        private async Task<Member> CreateMemberFromUserDetails()
        {
            Organization organization = null;
            Member registeredMember = null;

            try
            {
                var organizationDetails = await OrganizationService.GetOrganizationsByNameAsync(user.OrganizationName);
                organization = organizationDetails.FirstOrDefault();

                if (organization == null)
                {
                    organization = new Organization
                    {
                        OrganizationId = Guid.NewGuid(),
                        OrganizationName = user.OrganizationName
                    };
                    roles = await _RoleslistService.GetAllRoleNamesAsync();
                    var createdOrganization = await OrganizationService.RegisterOrganizationsAsync(organization);

                    if (createdOrganization == null)
                    {
                        logger.LogError(Localizer["OrganizationCreationFailed"]);
                        throw new Exception(Localizer["OrganizationCreationFailedMessage"]);
                    }

                    logger.LogInformation(Localizer["NewOrganizationCreated"], createdOrganization.OrganizationId);
                }

                registeredMember = new Member
                {
                    Id = Guid.TryParse(user.id, out var userId) ? userId : Guid.NewGuid(),
                    Email = user.mail,
                    FirstName = user.givenName,
                    LastName = user.surname,
                    UserName = $"{user.givenName}{user.surname}",
                    PhoneNumber = user.mobilePhone,
                    Country = user.country,
                    OrganizationID = organization.OrganizationId,
                    OrganizationName = user.OrganizationName,
                    Subscription = Subscription,
                    Address = new Address
                    {
                        AddressLine1 = user.streetAddress,
                        PostalCode = user.postalCode,
                        State = user.state,
                        Country = user.country,
                        Subscription = Subscription,
                    },
                    IsActive = true
                };

                var rolesdata = await RoleService.GetAllRolesByOrgIdAsync(organization.OrganizationId, Subscription);
                if (rolesdata.Count == zero)
                {
                    foreach (var role in roles)
                    {
                        var newRole = new Role
                        {
                            RoleId = Guid.NewGuid(),
                            RoleName = role,
                            CreatedDate = DateTime.Now,
                            IsActive = true,
                            UpdatedDate = DateTime.Now,
                            UpdatedBy = Guid.Parse(user.id),
                            OrganizationId = organization.OrganizationId,
                            Subscription = Subscription
                        };
                        await RoleService.RegisterRoleAsync(newRole);

                        if (role == "Admin")
                        {
                            registeredMember.RoleID = newRole.RoleId;
                            registeredMember.RoleName = "Admin";
                        }
                    }
                    var pagePaths = await PagePathService.GetPagePathsAsync();
                    var pageUrls = pagePaths.Select(p => new PageRoleMappingData
                    {
                        PagePath = p.PagePathValue
                    }).ToList();
                    foreach (var page in pageUrls)
                    {
                        var newMapping = new PageRoleMappingData
                        {
                            Id = Guid.NewGuid(),
                            PagePath = page.PagePath,
                            RoleId = registeredMember.RoleID ?? Guid.Empty,
                            RoleName = registeredMember.RoleName,
                            OrganizationId = organization.OrganizationId,
                            CreatedBy = Guid.Parse(user.id),
                            CreatedDate = DateTime.Now,
                            IsActive = true,
                            HasAccess = true,
                            Subscription = Subscription
                        };

                        await PageRoleMappingService.AddPageRoleMappingAsync(newMapping);
                    }

                    logger.LogInformation(Localizer["PageRoleMappingsCreatedSuccessfully"]);
                }
                else
                {
                    var adminRole = rolesdata.FirstOrDefault(r => r.RoleName == "Admin");
                    if (adminRole != null)
                    {
                        registeredMember.RoleID = adminRole.RoleId;
                        registeredMember.RoleName = "Admin";
                    }
                }

                predefinedvisittypes = await _PredefinedVisitTypeService.GetAllPredefinedVisitTypesAsync();
                visittypes = await _VisitTypeService.GetVisitTypesByOrganizationIdAsync(organization.OrganizationId, Subscription);

                if (visittypes == null || !visittypes.Any())
                {
                    foreach (var predefined in predefinedvisittypes)
                    {
                        var newVisit = new VisitType
                        {
                            ID = Guid.NewGuid(),
                            OrganizationId = organization.OrganizationId,
                            VisitName = predefined.VisitName,
                            CPTCode = predefined.CPTCode,
                            Subscription = Subscription
                        };
                        await _VisitTypeService.AddVisitTypeAsync(newVisit);
                    }
                }

                var updateFields = new Dictionary<string, object>
                {
                { "displayName", registeredMember.UserName }
                };

                bool updateSuccessful = await GraphApiService.UpdateUserProfileAsync(user.id, updateFields);
                if (!updateSuccessful)
                {
                    logger.LogError(Localizer["FailedToUpdateDisplayName"]);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, Localizer["ErrorProcessingOrganization"], ex.Message);
            }

            return registeredMember;
        }
    }
}

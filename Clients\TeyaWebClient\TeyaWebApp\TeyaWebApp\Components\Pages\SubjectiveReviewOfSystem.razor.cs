﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class SubjectiveReviewOfSystem
    {
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] public IReviewOfSystemService _ReviewOfSytemService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        private MudDialog _reviewofsystem;
        private DateTime? _CreatedDate = DateTime.Now;
        private DateTime? _UpdatedDate=DateTime.Now;
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private List<ReviewOfSystem> reviewofsytem { get; set; } = new List<ReviewOfSystem>();
        private SfRichTextEditor RichTextEditor;
        public SfGrid<ReviewOfSystem> ReviewOfSytemGrid { get; set; }
        public Guid PatientId { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        public List<ReviewOfSystem> deleteList = new List<ReviewOfSystem>();
        public List<ReviewOfSystem> AddList = new List<ReviewOfSystem>();
        private string? _Description = "Enter Additional Reviews";
        /// <summary>
        /// Initializes the component asynchronously by setting the Patient ID,
        /// fetching the active review of system records, and formatting the editor content.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            reviewofsytem = await _ReviewOfSytemService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            editorContent = string.Join("<br>", reviewofsytem.Select(m =>
            $"Created Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}, " +
            $"Description: {(string.IsNullOrEmpty(m.Decription) ? Localizer["Enter Additional Reviews"] : m.Decription)}, " +
            $"Chest Pain: {(m.ChestPain.HasValue ? (m.ChestPain.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])}, " +
            $"Congestion: {(m.Congestion.HasValue ? (m.Congestion.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])} ," +
            $"Itchy Eyes: {(m.ItchyEyes.HasValue ? (m.ItchyEyes.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])}" 
            ));
        }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };
        /// <summary>
        /// Creates a dropdown template for selecting boolean values in the ReviewOfSystem grid.
        /// </summary>
        private RenderFragment<object> CreateDropdownTemplate(Func<ReviewOfSystem, bool?> getValue, Action<ReviewOfSystem, bool?> setValue)
        {
            return (context) => (builder) =>
            {
                if (context is not ReviewOfSystem review) return;

                builder.OpenComponent<SfDropDownList<bool?, bool?>>(0);
                builder.AddAttribute(1, "DataSource", new List<bool?> { true, false });
                builder.AddAttribute(2, "Value", getValue(review));
                builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<bool?>(this, value => setValue(review, value)));
                builder.AddAttribute(4, "Placeholder", "Select Option");
                builder.CloseComponent();
            };
        }
        /// <summary>
        /// Creates a dropdown template for selecting boolean values in the ReviewOfSystem grid.
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private RenderFragment<object> IsChestPainEditTemplate => CreateDropdownTemplate(
            review => review.ChestPain,
            (review, value) => review.ChestPain = value
        );

        private RenderFragment<object> IsCongestionEditTemplate => CreateDropdownTemplate(
            review => review.Congestion,
            (review, value) => review.Congestion = value
        );

        private RenderFragment<object> IsItchyEyesEditTemplate => CreateDropdownTemplate(
            review => review.ItchyEyes,
            (review, value) => review.ItchyEyes = value
        );
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task OpenNewDialogBox()
        {
            await _reviewofsystem.ShowAsync();
        }

        /// <summary>
        /// Closes the new dialog box after resetting input fields.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _reviewofsystem.CloseAsync();
        }

        /// <summary>
        /// Adds a new history record to the ReviewOfSystem list and refreshes the grid.
        /// </summary>
        private async void AddNewHistory()
        {
            var newHistory = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = PatientId,
                OrganizationId = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Decription = _Description,
                IsActive = true,
            };

            AddList.Add(newHistory);
            reviewofsytem.Add(newHistory);
            await ReviewOfSytemGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Resets the input fields to their default values.
        /// </summary>
        private void ResetInputFields()
        {
            _CreatedDate = null;
        }



        /// <summary>
        /// Handles the action completion event for the ReviewOfSystem grid.
        /// If an item is deleted, it is either removed from the add list or marked as inactive and added to the delete list.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public void ActionCompletedHandler(ActionEventArgs<ReviewOfSystem> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleteReviewOfSystem = args.Data as ReviewOfSystem;
                var existingItem = AddList.FirstOrDefault(m => m.ReviewOfSystemId == deleteReviewOfSystem.ReviewOfSystemId);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// Handles the beginning of an action for the ActiveMedication grid.
        /// If the action is a save operation, it updates the UpdatedDate.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public void ActionBeginHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Cancels the data operation, clears lists, reloads data, and resets input fields.
        /// </summary>
        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            reviewofsytem = await _ReviewOfSytemService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Saves the data by adding new items and updating existing ones.
        /// Updates the editor content with formatted review system data.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _ReviewOfSytemService.AddReviewOfSystemAsync(AddList, OrgID, Subscription);
            }
            await _ReviewOfSytemService.UpdateReviewOfSystemListAsync(reviewofsytem,OrgID, Subscription);
            await _ReviewOfSytemService.UpdateReviewOfSystemListAsync(deleteList, OrgID, Subscription);
            deleteList.Clear();
            AddList.Clear();
            editorContent = string.Join("<br>", reviewofsytem.Select(m =>
            $"Created Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}, " +
            $"Description: {(string.IsNullOrEmpty(m.Decription) ? Localizer["Enter Additional Reviews"] : m.Decription)}, " +
            $"Chest Pain: {(m.ChestPain.HasValue ? (m.ChestPain.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])}, " +
            $"Congestion: {(m.Congestion.HasValue ? (m.Congestion.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])} ," +
            $"Itchy Eyes: {(m.ItchyEyes.HasValue ? (m.ItchyEyes.Value ? Localizer["Yes"] : Localizer["No"]) : Localizer["Unknown"])}"
           ));

            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

    }
}
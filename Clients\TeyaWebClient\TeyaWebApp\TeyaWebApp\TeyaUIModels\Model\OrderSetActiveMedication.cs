﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class OrderSetActiveMedication : IModel
    {
        public Guid MedicineId { get; set; }
        public string? BrandName { get; set; }
        public string? DrugDetails { get; set; }
        public string? Quantity { get; set; }
        public string? Frequency { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? CheifComplaint { get; set; }
        public Guid? CheifComplaintId { get; set; }
        public Guid OrderSetId { get; set; }

    }
}
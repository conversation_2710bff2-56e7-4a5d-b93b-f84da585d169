﻿using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class TemplateService : ITemplateService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public TemplateService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<TemplateData>> GetTemplatesAsync(Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Templates/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TemplateData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }
        public async Task<List<TemplateData>> GetTemplatesByIdAsync(Guid Id, Guid? OrgID, bool Subscription)
        {
            var Url = $"{_EncounterNotes}/api/Templates/{Id}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, Url);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<TemplateData>>(responseData, options);
            }
            return null;
        }
        public async Task<List<TemplateData>> GetTemplatesByPCPIdAsync(Guid PCPId, Guid? OrgID, bool Subscription)
        {
            var Url = $"{_EncounterNotes}/api/Templates/ByPCP/{PCPId}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, Url);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<TemplateData>>(responseData, options);
            }
            return null;
        }

        public async Task CreateTemplatesAsync(TemplateData templates, Guid? OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(new List<TemplateData> { templates });
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var Url = $"{_EncounterNotes}/api/Templates/Templates/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, Url)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                throw new HttpRequestException(_localizer["TemplateRetrievalFailure"]);
            }
        }
        public async Task UpdateTemplatesAsync(List<TemplateData> templates, Guid? OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/Templates/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(templates);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task DeleteTemplatesAsync(Guid templateId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Templates/{templateId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }
    }
}
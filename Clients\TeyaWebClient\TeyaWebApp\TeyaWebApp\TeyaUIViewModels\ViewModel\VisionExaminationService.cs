﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class VisionExaminationService : IVisionExaminationService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<VisionExaminationService> _localizer;
        private readonly ILogger<VisionExaminationService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public VisionExaminationService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<VisionExaminationService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active VisionExamination Record by Id 
        /// </summary>
        public async Task<List<VisionRx>> GetVisionExaminationByIdAsyncAndIsActive(Guid id, Guid OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/VisionExamination/{id}/{OrgID}/{Subscription}/isActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<VisionRx>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["VisionRecordRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Get All VisionExamination Record by Id 
        /// </summary>
        public async Task<List<VisionRx>> GetVisionExaminationAsync(Guid id, Guid OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/VisionExamination/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<VisionRx>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["VisionRecordRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update an existing Record
        /// </summary>
        public async Task UpdateVisionRecordsAsync(VisionRx visionRx)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/VisionExamination/{visionRx.ExaminationId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(visionRx);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task<List<VisionRx>> GetVisionRecordsAsync(Guid OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/VisionExamination/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<VisionRx>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RelationRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Delete an existing Record By Id
        /// </summary>
        public async Task DeleteVisionRecordsAsync(Guid MemberId, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/VisionExamination/{MemberId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }

        /// <summary>
        /// Add new VisionExamination Record
        /// </summary>
        public async Task CreateVisionExaminationAsync(VisionRx Member)
        {
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Member);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var apiUrl = $"{_EncounterNotes}/api/VisionExamination";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };

            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["CreatingVisionRecordFailure"]);
            }
        }
    }
}

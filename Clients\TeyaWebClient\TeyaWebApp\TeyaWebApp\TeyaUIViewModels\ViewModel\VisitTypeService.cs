﻿using Azure;
using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModels
{
    /// <summary>
    /// Service class to handle visit type-related operations.
    /// </summary>
    public class VisitTypeService : IVisitTypeService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<VisitTypeService> _localizer;
        private readonly ILogger<VisitTypeService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="VisitTypeService"/> class.
        /// </summary>
        /// <param name="httpClient">The HTTP client for making API requests.</param>
        /// <param name="localizer">The localizer for handling localized messages.</param>
        /// <param name="logger">The logger for logging errors and information.</param>
        /// <param name="tokenService">The token service for handling authentication tokens.</param>
        public VisitTypeService(HttpClient httpClient, IStringLocalizer<VisitTypeService> localizer, ILogger<VisitTypeService> logger, ITokenService tokenService)
        {
            _tokenService = tokenService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Retrieves a list of visit type names asynchronously.
        /// </summary>
        /// <returns>A list of visit type names.</returns>
        /// <exception cref="Exception">Throws an exception if an error occurs while fetching data.</exception>
        public async Task<List<string>> GetVisitTypeNamesAsync(Guid? orgId)
        {
            try
            {
                var allVisitTypes = await GetAllVisitTypesAsync();
                return allVisitTypes?
                    .Where(visitType => visitType.OrganizationId == orgId)
                    .Select(visitType => visitType.VisitName)
                    .Distinct()
                    .ToList() ?? new List<string>();
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, _localizer["An error occurred while fetching visit type names."]);
                throw;
            }
        }

        /// <summary>
        /// Retrieves all visit types asynchronously.
        /// </summary>
        /// <returns>A list of <see cref="VisitType"/> objects.</returns>
        /// <exception cref="UnauthorizedAccessException">Thrown when the access token is missing.</exception>
        /// <exception cref="Exception">Throws an exception if an error occurs while fetching visit types.</exception>
        public async Task<List<VisitType>> GetAllVisitTypesAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/VisitType";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<VisitType>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingVisitTypes"]);
                throw;
            }
        }

        /// <summary>
        /// Retrieves visit types by Organization ID asynchronously.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <returns>A list of visit types associated with the given Organization ID.</returns>
        public async Task<List<VisitType>> GetVisitTypesByOrganizationIdAsync(Guid orgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/VisitType/by-organization/{orgId}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<VisitType>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingVisitTypes"]);
                throw;
            }
        }

        /// <summary>
        /// Updates the CPT code for a specific visit type by visit name and organization ID.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <param name="newCptCode">The new CPT code.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var requestUrl = $"{_MemberService}/api/VisitType/update-cpt-code/{orgId}/{Uri.EscapeDataString(visitName)}/{Uri.EscapeDataString(newCptCode)}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, requestUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingCptCode"]);
                throw;
            }
        }

        /// <summary>
        /// Deletes a visit type for a specific organization based on the visit name.
        /// </summary>
        /// <param name="orgId">The unique identifier of the organization.</param>
        /// <param name="visitName">The name of the visit type to be deleted.</param>
        /// <returns>Returns true if the deletion was successful, otherwise false.</returns>
        /// <exception cref="UnauthorizedAccessException">Thrown when the access token is missing.</exception>
        /// <exception cref="HttpRequestException">Thrown when the HTTP request fails.</exception>
        /// <exception cref="Exception">Logs and rethrows any unexpected errors.</exception>
        public async Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var requestUrl = $"{_MemberService}/api/VisitType/delete/{orgId}/{Uri.EscapeDataString(visitName)}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingVisitType"]);
                throw;
            }
        }

        /// <summary>
        /// Adds a new visit type asynchronously.
        /// </summary>
        /// <param name="visitType">The visit type details.</param>
        /// <returns>True if the addition was successful, otherwise false.</returns>
        public async Task<bool> AddVisitTypeAsync(VisitType visitType)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/VisitType";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = new StringContent(JsonSerializer.Serialize(visitType), System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingVisitType"]);
                throw;
            }
        }
    }
}

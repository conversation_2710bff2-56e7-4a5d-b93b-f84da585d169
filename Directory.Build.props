<Project>
  <PropertyGroup>
    <!-- Build Versioning Configuration -->
    <!-- Version format: YYYY.MM.DD.HHMM (e.g., 2024.12.19.1430) -->
    
    <!-- Get current date and time -->
    <BuildYear>$([System.DateTime]::Now.ToString("yyyy"))</BuildYear>
    <BuildMonth>$([System.DateTime]::Now.ToString("MM"))</BuildMonth>
    <BuildDay>$([System.DateTime]::Now.ToString("dd"))</BuildDay>
    <BuildTime>$([System.DateTime]::Now.ToString("HHmm"))</BuildTime>
    
    <!-- Construct version numbers -->
    <TeyaBuildVersion>$(BuildYear).$(BuildMonth).$(BuildDay).$(BuildTime)</TeyaBuildVersion>
    
    <!-- Set standard .NET version properties -->
    <Version>$(TeyaBuildVersion)</Version>
    <AssemblyVersion>$(BuildYear).$(BuildMonth).$(BuildDay).0</AssemblyVersion>
    <FileVersion>$(TeyaBuildVersion)</FileVersion>
    <InformationalVersion>$(TeyaBuildVersion)</InformationalVersion>
    
    <!-- Additional metadata -->
    <Product>Teya Health Platform</Product>
    <Company>Teya Health</Company>
    <Copyright>Copyright © Teya Health $([System.DateTime]::Now.ToString("yyyy"))</Copyright>
    
    <!-- Build configuration -->
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
  </PropertyGroup>

  <!-- Display version information during build -->
  <Target Name="DisplayVersionInfo" BeforeTargets="Build">
    <Message Text="Building with version: $(TeyaBuildVersion)" Importance="high" />
    <Message Text="Build timestamp: $([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))" Importance="high" />
  </Target>

  <!-- Create version info file for runtime access -->
  <Target Name="CreateVersionFile" BeforeTargets="Build">
    <PropertyGroup>
      <VersionFileContent>
{
  "version": "$(TeyaBuildVersion)",
  "buildDate": "$([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))",
  "buildYear": "$(BuildYear)",
  "buildMonth": "$(BuildMonth)",
  "buildDay": "$(BuildDay)",
  "buildTime": "$(BuildTime)"
}
      </VersionFileContent>
    </PropertyGroup>
    
    <!-- Write version file to output directory for web projects -->
    <WriteLinesToFile 
      File="$(OutputPath)version.json" 
      Lines="$(VersionFileContent)" 
      Overwrite="true" 
      Condition="'$(OutputType)' == 'Exe' OR '$(OutputType)' == 'Library'" />
  </Target>
</Project>

<Project>
  <PropertyGroup>
    <!-- Build Versioning Configuration -->
    <!-- Version format: Major.Minor.Patch.BuildNumber (e.g., 1.0.0.123) -->

    <!-- Version components -->
    <MajorVersion Condition="'$(MajorVersion)' == ''">1</MajorVersion>
    <MinorVersion Condition="'$(MinorVersion)' == ''">0</MinorVersion>
    <PatchVersion Condition="'$(PatchVersion)' == ''">0</PatchVersion>

    <!-- Build number file path -->
    <BuildNumberFile>$(MSBuildThisFileDirectory)build-number.txt</BuildNumberFile>

    <!-- Read current build number or start from 1 -->
    <CurrentBuildNumber Condition="Exists('$(BuildNumberFile)')">$([System.IO.File]::ReadAllText('$(BuildNumberFile)').Trim())</CurrentBuildNumber>
    <CurrentBuildNumber Condition="'$(CurrentBuildNumber)' == '' OR !Exists('$(BuildNumberFile)')">1</CurrentBuildNumber>

    <!-- Construct version numbers -->
    <TeyaBuildVersion>$(MajorVersion).$(MinorVersion).$(PatchVersion).$(CurrentBuildNumber)</TeyaBuildVersion>

    <!-- Set standard .NET version properties -->
    <Version>$(TeyaBuildVersion)</Version>
    <AssemblyVersion>$(MajorVersion).$(MinorVersion).$(PatchVersion).0</AssemblyVersion>
    <FileVersion>$(TeyaBuildVersion)</FileVersion>
    <InformationalVersion>$(TeyaBuildVersion)</InformationalVersion>

    <!-- Additional metadata -->
    <Product>Teya Health Platform</Product>
    <Company>Teya Health</Company>
    <Copyright>Copyright © Teya Health $([System.DateTime]::Now.ToString("yyyy"))</Copyright>

    <!-- Build configuration -->
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
  </PropertyGroup>

  <!-- Increment build number before build -->
  <Target Name="IncrementBuildNumber" BeforeTargets="Build">
    <PropertyGroup>
      <NextBuildNumber>$([MSBuild]::Add($(CurrentBuildNumber), 1))</NextBuildNumber>
    </PropertyGroup>

    <!-- Write the incremented build number back to file -->
    <WriteLinesToFile
      File="$(BuildNumberFile)"
      Lines="$(NextBuildNumber)"
      Overwrite="true" />

    <!-- Update the current build number for this build -->
    <PropertyGroup>
      <CurrentBuildNumber>$(NextBuildNumber)</CurrentBuildNumber>
      <TeyaBuildVersion>$(MajorVersion).$(MinorVersion).$(PatchVersion).$(CurrentBuildNumber)</TeyaBuildVersion>
      <Version>$(TeyaBuildVersion)</Version>
      <FileVersion>$(TeyaBuildVersion)</FileVersion>
      <InformationalVersion>$(TeyaBuildVersion)</InformationalVersion>
    </PropertyGroup>
  </Target>

  <!-- Display version information during build -->
  <Target Name="DisplayVersionInfo" BeforeTargets="Build" DependsOnTargets="IncrementBuildNumber">
    <Message Text="Building with version: $(TeyaBuildVersion)" Importance="high" />
    <Message Text="Build number: $(CurrentBuildNumber)" Importance="high" />
    <Message Text="Build timestamp: $([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))" Importance="high" />
  </Target>

  <!-- Create version info file for runtime access -->
  <Target Name="CreateVersionFile" BeforeTargets="Build" DependsOnTargets="IncrementBuildNumber">
    <PropertyGroup>
      <VersionFileContent>
{
  "version": "$(TeyaBuildVersion)",
  "buildNumber": $(CurrentBuildNumber),
  "majorVersion": $(MajorVersion),
  "minorVersion": $(MinorVersion),
  "patchVersion": $(PatchVersion),
  "buildDate": "$([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))"
}
      </VersionFileContent>
    </PropertyGroup>

    <!-- Write version file to output directory for web projects -->
    <WriteLinesToFile
      File="$(OutputPath)version.json"
      Lines="$(VersionFileContent)"
      Overwrite="true"
      Condition="'$(OutputType)' == 'Exe' OR '$(OutputType)' == 'Library'" />
  </Target>
</Project>

<Project>
  <PropertyGroup>
    <!-- Microsoft-Style Build Versioning System -->
    <!-- Exact pattern used by Windows, Visual Studio, Office -->
    <!-- Format: Major.Minor.Build.Revision -->

    <!-- Product Version (like Windows 10.0, VS 17.0) -->
    <MajorVersion Condition="'$(MajorVersion)' == ''">1</MajorVersion>
    <MinorVersion Condition="'$(MinorVersion)' == ''">0</MinorVersion>

    <!-- Microsoft Build Number Pattern -->
    <!-- Build = YMMDD (Year + Month + Day, like Microsoft uses) -->
    <!-- Example: 50605 = Year 2025 (last digit 5), Month 06, Day 05 -->
    <BuildYear>$([System.DateTime]::Now.Year.ToString().Substring(3, 1))</BuildYear>
    <BuildMonth>$([System.DateTime]::Now.ToString("MM"))</BuildMonth>
    <BuildDay>$([System.DateTime]::Now.ToString("dd"))</BuildDay>
    <MicrosoftBuildNumber>$(BuildYear)$(BuildMonth)$(BuildDay)</MicrosoftBuildNumber>

    <!-- Microsoft Revision Pattern -->
    <!-- Revision = Build counter that increments throughout the day -->
    <BuildCounterFile>$(MSBuildThisFileDirectory).build\build-counter.txt</BuildCounterFile>
    <BuildCounterDir>$(MSBuildThisFileDirectory).build</BuildCounterDir>

    <!-- Read current build counter -->
    <BuildCounter Condition="Exists('$(BuildCounterFile)')">$([System.IO.File]::ReadAllText('$(BuildCounterFile)').Trim())</BuildCounter>
    <BuildCounter Condition="'$(BuildCounter)' == '' OR !Exists('$(BuildCounterFile)')">1</BuildCounter>

    <!-- Microsoft Version Format: Major.Minor.Build.Revision -->
    <!-- Examples: 10.0.19041.1234 (Windows), 17.4.33213.308 (VS) -->
    <MicrosoftVersion>$(MajorVersion).$(MinorVersion).$(MicrosoftBuildNumber).$(BuildCounter)</MicrosoftVersion>

    <!-- .NET Version Properties -->
    <Version>$(MicrosoftVersion)</Version>
    <AssemblyVersion>$(MajorVersion).$(MinorVersion).0.0</AssemblyVersion>
    <FileVersion>$(MicrosoftVersion)</FileVersion>
    <InformationalVersion>$(MicrosoftVersion)</InformationalVersion>

    <!-- Microsoft-style Product Information -->
    <Product>Teya Health Platform</Product>
    <Company>Teya Health Corporation</Company>
    <Copyright>© Teya Health Corporation. All rights reserved.</Copyright>
    <Description>Enterprise Healthcare Management Platform</Description>

    <!-- Build Configuration -->
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
  </PropertyGroup>

  <!-- Microsoft-Style Build Counter Management -->
  <Target Name="IncrementMicrosoftBuildCounter" BeforeTargets="Build">
    <!-- Ensure build directory exists -->
    <MakeDir Directories="$(BuildCounterDir)" Condition="!Exists('$(BuildCounterDir)')" />

    <!-- Check if we need to reset counter for new day (Microsoft pattern) -->
    <PropertyGroup>
      <TodayFile>$(BuildCounterDir)\today.txt</TodayFile>
      <Today>$(MicrosoftBuildNumber)</Today>
      <LastBuildDay Condition="Exists('$(TodayFile)')">$([System.IO.File]::ReadAllText('$(TodayFile)').Trim())</LastBuildDay>
      <LastBuildDay Condition="'$(LastBuildDay)' == ''">0</LastBuildDay>
    </PropertyGroup>

    <!-- Reset counter if new day (like Microsoft does) -->
    <PropertyGroup Condition="'$(Today)' != '$(LastBuildDay)'">
      <BuildCounter>1</BuildCounter>
    </PropertyGroup>

    <!-- Increment counter -->
    <PropertyGroup>
      <NextBuildCounter>$([MSBuild]::Add($(BuildCounter), 1))</NextBuildCounter>
    </PropertyGroup>

    <!-- Write updated counter and today marker -->
    <WriteLinesToFile File="$(BuildCounterFile)" Lines="$(NextBuildCounter)" Overwrite="true" />
    <WriteLinesToFile File="$(TodayFile)" Lines="$(Today)" Overwrite="true" />

    <!-- Update Microsoft version with new counter -->
    <PropertyGroup>
      <BuildCounter>$(NextBuildCounter)</BuildCounter>
      <MicrosoftVersion>$(MajorVersion).$(MinorVersion).$(MicrosoftBuildNumber).$(BuildCounter)</MicrosoftVersion>
      <Version>$(MicrosoftVersion)</Version>
      <FileVersion>$(MicrosoftVersion)</FileVersion>
      <InformationalVersion>$(MicrosoftVersion)</InformationalVersion>
    </PropertyGroup>
  </Target>

  <!-- Display Microsoft-Style Build Information -->
  <Target Name="DisplayMicrosoftBuildInfo" BeforeTargets="Build" DependsOnTargets="IncrementMicrosoftBuildCounter">
    <Message Text="==================== TEYA HEALTH BUILD ====================" Importance="high" />
    <Message Text="Microsoft-Style Version: $(MicrosoftVersion)" Importance="high" />
    <Message Text="Product Version: $(MajorVersion).$(MinorVersion)" Importance="high" />
    <Message Text="Build Number: $(MicrosoftBuildNumber) (202$(BuildYear)/$(BuildMonth)/$(BuildDay))" Importance="high" />
    <Message Text="Revision: $(BuildCounter) (Build #$(BuildCounter) today)" Importance="high" />
    <Message Text="Configuration: $(Configuration)" Importance="high" />
    <Message Text="Build Time: $([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))" Importance="high" />
    <Message Text="=========================================================" Importance="high" />
  </Target>

  <!-- Create Microsoft-Style Version File -->
  <Target Name="CreateMicrosoftVersionFile" BeforeTargets="Build" DependsOnTargets="IncrementMicrosoftBuildCounter">
    <PropertyGroup>
      <MicrosoftVersionContent>
{
  "product": {
    "name": "$(Product)",
    "company": "$(Company)",
    "version": "$(MajorVersion).$(MinorVersion)",
    "fullVersion": "$(MicrosoftVersion)"
  },
  "microsoft": {
    "buildNumber": "$(MicrosoftBuildNumber)",
    "revision": $(BuildCounter),
    "year": "20$(BuildYear)",
    "month": "$(BuildMonth)",
    "day": "$(BuildDay)",
    "pattern": "Microsoft Windows/Visual Studio Style"
  },
  "build": {
    "configuration": "$(Configuration)",
    "timestamp": "$([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))",
    "machine": "$([System.Environment]::MachineName)"
  },
  "version": {
    "major": $(MajorVersion),
    "minor": $(MinorVersion),
    "build": $(MicrosoftBuildNumber),
    "revision": $(BuildCounter),
    "full": "$(MicrosoftVersion)"
  }
}
      </MicrosoftVersionContent>
    </PropertyGroup>

    <!-- Write Microsoft-style version file -->
    <WriteLinesToFile
      File="$(OutputPath)version.json"
      Lines="$(MicrosoftVersionContent)"
      Overwrite="true"
      Condition="'$(OutputType)' == 'Exe' OR '$(OutputType)' == 'Library'" />
  </Target>
</Project>

<Project>
  <PropertyGroup>
    <!-- TeyaHealth Enterprise Build Versioning System -->
    <!-- Professional pattern following industry standards -->
    <!-- Format: Major.Minor.Build.Revision -->

    <!-- Product Version -->
    <MajorVersion Condition="'$(MajorVersion)' == ''">1</MajorVersion>
    <MinorVersion Condition="'$(MinorVersion)' == ''">0</MinorVersion>

    <!-- TeyaHealth Build Number Pattern -->
    <!-- Build = YMMDD (Year + Month + Day) -->
    <!-- Example: 50605 = Year 2025 (last digit 5), Month 06, Day 05 -->
    <BuildYear>$([System.DateTime]::Now.Year.ToString().Substring(3, 1))</BuildYear>
    <BuildMonth>$([System.DateTime]::Now.ToString("MM"))</BuildMonth>
    <BuildDay>$([System.DateTime]::Now.ToString("dd"))</BuildDay>
    <TeyaBuildNumber>$(BuildYear)$(BuildMonth)$(BuildDay)</TeyaBuildNumber>

    <!-- TeyaHealth Revision Pattern -->
    <!-- Revision = Build counter that increments throughout the day -->
    <BuildCounterFile>$(MSBuildThisFileDirectory).build\build-counter.txt</BuildCounterFile>
    <BuildCounterDir>$(MSBuildThisFileDirectory).build</BuildCounterDir>

    <!-- Read current build counter -->
    <BuildCounter Condition="Exists('$(BuildCounterFile)')">$([System.IO.File]::ReadAllText('$(BuildCounterFile)').Trim())</BuildCounter>
    <BuildCounter Condition="'$(BuildCounter)' == '' OR !Exists('$(BuildCounterFile)')">1</BuildCounter>

    <!-- TeyaHealth Version Format: Major.Minor.Build.Revision -->
    <!-- Example: 1.0.50605.4 -->
    <TeyaVersion>$(MajorVersion).$(MinorVersion).$(TeyaBuildNumber).$(BuildCounter)</TeyaVersion>

    <!-- .NET Version Properties -->
    <Version>$(TeyaVersion)</Version>
    <AssemblyVersion>$(MajorVersion).$(MinorVersion).0.0</AssemblyVersion>
    <FileVersion>$(TeyaVersion)</FileVersion>
    <InformationalVersion>$(TeyaVersion)</InformationalVersion>

    <!-- TeyaHealth Product Information -->
    <Product>Teya Health Platform</Product>
    <Company>Teya Health Corporation</Company>
    <Copyright>© Teya Health Corporation. All rights reserved.</Copyright>
    <Description>Enterprise Healthcare Management Platform</Description>

    <!-- Build Configuration -->
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
  </PropertyGroup>

  <!-- TeyaHealth Build Counter Management -->
  <Target Name="IncrementTeyaBuildCounter" BeforeTargets="Build">
    <!-- Ensure build directory exists -->
    <MakeDir Directories="$(BuildCounterDir)" Condition="!Exists('$(BuildCounterDir)')" />

    <!-- Check if we need to reset counter for new day -->
    <PropertyGroup>
      <TodayFile>$(BuildCounterDir)\today.txt</TodayFile>
      <Today>$(TeyaBuildNumber)</Today>
      <LastBuildDay Condition="Exists('$(TodayFile)')">$([System.IO.File]::ReadAllText('$(TodayFile)').Trim())</LastBuildDay>
      <LastBuildDay Condition="'$(LastBuildDay)' == ''">0</LastBuildDay>
    </PropertyGroup>

    <!-- Reset counter if new day -->
    <PropertyGroup Condition="'$(Today)' != '$(LastBuildDay)'">
      <BuildCounter>1</BuildCounter>
    </PropertyGroup>

    <!-- Increment counter -->
    <PropertyGroup>
      <NextBuildCounter>$([MSBuild]::Add($(BuildCounter), 1))</NextBuildCounter>
    </PropertyGroup>

    <!-- Write updated counter and today marker -->
    <WriteLinesToFile File="$(BuildCounterFile)" Lines="$(NextBuildCounter)" Overwrite="true" />
    <WriteLinesToFile File="$(TodayFile)" Lines="$(Today)" Overwrite="true" />

    <!-- Update TeyaHealth version with new counter -->
    <PropertyGroup>
      <BuildCounter>$(NextBuildCounter)</BuildCounter>
      <TeyaVersion>$(MajorVersion).$(MinorVersion).$(TeyaBuildNumber).$(BuildCounter)</TeyaVersion>
      <Version>$(TeyaVersion)</Version>
      <FileVersion>$(TeyaVersion)</FileVersion>
      <InformationalVersion>$(TeyaVersion)</InformationalVersion>
    </PropertyGroup>
  </Target>

  <!-- Display TeyaHealth Build Information -->
  <Target Name="DisplayTeyaBuildInfo" BeforeTargets="Build" DependsOnTargets="IncrementTeyaBuildCounter">
    <Message Text="==================== TEYA HEALTH BUILD ====================" Importance="high" />
    <Message Text="Version: $(TeyaVersion)" Importance="high" />
    <Message Text="Product Version: $(MajorVersion).$(MinorVersion)" Importance="high" />
    <Message Text="Build Number: $(TeyaBuildNumber) (202$(BuildYear)/$(BuildMonth)/$(BuildDay))" Importance="high" />
    <Message Text="Revision: $(BuildCounter) (Build #$(BuildCounter) today)" Importance="high" />
    <Message Text="Configuration: $(Configuration)" Importance="high" />
    <Message Text="Build Time: $([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))" Importance="high" />
    <Message Text="=========================================================" Importance="high" />
  </Target>

  <!-- Create TeyaHealth Version File -->
  <Target Name="CreateTeyaVersionFile" BeforeTargets="Build" DependsOnTargets="IncrementTeyaBuildCounter">
    <PropertyGroup>
      <TeyaVersionContent>
{
  "product": {
    "name": "$(Product)",
    "company": "$(Company)",
    "version": "$(MajorVersion).$(MinorVersion)",
    "fullVersion": "$(TeyaVersion)"
  },
  "teyaHealth": {
    "buildNumber": "$(TeyaBuildNumber)",
    "revision": $(BuildCounter),
    "year": "202$(BuildYear)",
    "month": "$(BuildMonth)",
    "day": "$(BuildDay)",
    "pattern": "Enterprise Industry Standard"
  },
  "build": {
    "configuration": "$(Configuration)",
    "timestamp": "$([System.DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))",
    "machine": "$([System.Environment]::MachineName)"
  },
  "version": {
    "major": $(MajorVersion),
    "minor": $(MinorVersion),
    "build": $(TeyaBuildNumber),
    "revision": $(BuildCounter),
    "full": "$(TeyaVersion)"
  }
}
      </TeyaVersionContent>
    </PropertyGroup>

    <!-- Write TeyaHealth version file -->
    <WriteLinesToFile
      File="$(OutputPath)version.json"
      Lines="$(TeyaVersionContent)"
      Overwrite="true"
      Condition="'$(OutputType)' == 'Exe' OR '$(OutputType)' == 'Library'" />
  </Target>
</Project>

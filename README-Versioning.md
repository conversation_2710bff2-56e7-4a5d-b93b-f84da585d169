# Teya Health Build Versioning System

This document describes the automatic build versioning system implemented for the Teya Health platform.

## Overview

The versioning system automatically generates incremental build numbers for every build, following industry-standard semantic versioning practices. Each build gets a unique, incrementing build number that tracks the total number of builds performed.

## Version Format

**Format**: `Major.Minor.Patch.BuildNumber`

**Examples**:
- `1.0.0.1` - First build
- `*******5` - 25th build
- `*********` - 156th build with version 1.2.0

## Components

### 1. Directory.Build.props
- **Location**: Root directory of the solution
- **Purpose**: Central MSBuild configuration that applies to all projects
- **Features**:
  - Automatic version generation based on current date/time
  - Sets all .NET version properties (Version, AssemblyVersion, FileVersion, etc.)
  - Creates version.json file in output directories
  - Displays version information during build

### 2. Build Scripts

#### PowerShell Script (Windows)
- **File**: `BuildScripts/BuildWithVersion.ps1`
- **Usage**: `.\BuildScripts\BuildWithVersion.ps1 [options]`

#### Bash <PERSON>ript (Linux/CI)
- **File**: `BuildScripts/BuildWithVersion.sh`
- **Usage**: `./BuildScripts/BuildWithVersion.sh [options]`

#### Version Helper Script
- **File**: `BuildScripts/GetVersion.ps1`
- **Usage**: `.\BuildScripts\GetVersion.ps1 [options]`

## Usage Examples

### Building the Entire Solution
```powershell
# Windows
.\BuildScripts\BuildWithVersion.ps1

# Linux/CI
./BuildScripts/BuildWithVersion.sh
```

### Building Specific Projects
```powershell
# Build Appointments API
.\BuildScripts\BuildWithVersion.ps1 -Project "Services/AppointmentsApi"

# Build Web App
.\BuildScripts\BuildWithVersion.ps1 -Project "Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp"
```

### Build Options
```powershell
# Debug build
.\BuildScripts\BuildWithVersion.ps1 -Configuration Debug

# Clean build
.\BuildScripts\BuildWithVersion.ps1 -Clean

# Build without package restore
.\BuildScripts\BuildWithVersion.ps1 -Restore:$false
```

### Getting Version Information
```powershell
# Show detailed version info
.\BuildScripts\GetVersion.ps1

# Get just the version number
.\BuildScripts\GetVersion.ps1 -Short

# Get version as JSON
.\BuildScripts\GetVersion.ps1 -Json
```

## Integration with Existing Builds

### Visual Studio
- The versioning system works automatically when building through Visual Studio
- No additional configuration required
- Version information is displayed in the build output

### Command Line
```bash
# Standard dotnet build commands work with automatic versioning
dotnet build
dotnet build --configuration Release
dotnet publish --configuration Release
```

### CI/CD Pipelines
The versioning system works seamlessly with existing CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Build with versioning
  run: ./BuildScripts/BuildWithVersion.sh -c Release
```

## Version Information Access

### Runtime Access
Each built project includes a `version.json` file in its output directory:

```json
{
  "version": "*******5",
  "buildNumber": 25,
  "majorVersion": 1,
  "minorVersion": 0,
  "patchVersion": 0,
  "buildDate": "2024-12-19 14:30:15"
}
```

### Assembly Information
Version information is embedded in assembly metadata:
- **AssemblyVersion**: `*******` (build number set to 0 for compatibility)
- **FileVersion**: `*******5` (full version with build number)
- **InformationalVersion**: `*******5` (full version with build number)

### Web Applications
For web applications, you can access version information at runtime:

```csharp
// Read version from file
var versionJson = File.ReadAllText(Path.Combine(AppContext.BaseDirectory, "version.json"));
var versionInfo = JsonSerializer.Deserialize<VersionInfo>(versionJson);

// Or from assembly
var assembly = Assembly.GetExecutingAssembly();
var version = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion;
```

## Managing Version Numbers

### Build Number File
The build number is stored in `build-number.txt` in the root directory. This file:
- Contains a single number representing the current build count
- Is automatically incremented with each build
- Can be manually edited to reset or adjust the build number

### Version Components
You can customize version components by setting MSBuild properties:

```bash
# Build with custom version components
dotnet build -p:MajorVersion=2 -p:MinorVersion=1 -p:PatchVersion=0

# This will produce version 2.1.0.X where X is the current build number
```

### Resetting Build Numbers
To reset the build number:
1. Edit `build-number.txt` and set it to your desired starting number
2. Or delete the file to start from 1

### Release Versioning
For releases, you can increment major/minor/patch versions:
- **Major**: Breaking changes (1.0.0 → 2.0.0)
- **Minor**: New features (1.0.0 → 1.1.0)
- **Patch**: Bug fixes (1.0.0 → 1.0.1)
- **Build**: Automatic increment (1.0.0.1 → *******)

## Benefits

1. **Industry Standard**: Uses semantic versioning like major companies
2. **Automatic**: Build numbers increment automatically
3. **Unique**: Every build gets a unique, sequential number
4. **Traceable**: Easy to identify build order and count
5. **Flexible**: Can customize major/minor/patch versions as needed
6. **Compatible**: Works with existing .NET tooling and CI/CD systems

## Troubleshooting

### Common Issues

1. **Permission Errors on Linux**:
   ```bash
   chmod +x BuildScripts/BuildWithVersion.sh
   ```

2. **PowerShell Execution Policy**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **Version File Not Created**:
   - Ensure the output directory exists
   - Check build output for any MSBuild errors

### Verification

To verify the versioning system is working:

1. Build any project
2. Check the build output for version messages
3. Look for `version.json` in the output directory
4. Use `GetVersion.ps1` to see current version

## Customization

### Changing Version Format
Edit `Directory.Build.props` to modify the version format:

```xml
<!-- Example: Add build number -->
<TeyaBuildVersion>$(BuildYear).$(BuildMonth).$(BuildDay).$(BuildTime).$(BuildNumber)</TeyaBuildVersion>
```

### Adding Custom Metadata
Add additional properties in `Directory.Build.props`:

```xml
<PropertyGroup>
  <BuildMachine>$([System.Environment]::MachineName)</BuildMachine>
  <BuildUser>$([System.Environment]::UserName)</BuildUser>
</PropertyGroup>
```

## Support

For questions or issues with the versioning system, please refer to this documentation or contact the development team.

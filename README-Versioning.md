# Teya Health Build Versioning System

This document describes the automatic build versioning system implemented for the Teya Health platform.

## Overview

The versioning system automatically generates version numbers based on the current date and time when building any project in the solution. This ensures that every build has a unique, chronologically ordered version number.

## Version Format

**Format**: `YYYY.MM.DD.HHMM`

**Examples**:
- `2024.12.19.1430` - Built on December 19, 2024 at 2:30 PM
- `2024.12.19.0915` - Built on December 19, 2024 at 9:15 AM

## Components

### 1. Directory.Build.props
- **Location**: Root directory of the solution
- **Purpose**: Central MSBuild configuration that applies to all projects
- **Features**:
  - Automatic version generation based on current date/time
  - Sets all .NET version properties (Version, AssemblyVersion, FileVersion, etc.)
  - Creates version.json file in output directories
  - Displays version information during build

### 2. Build Scripts

#### PowerShell Script (Windows)
- **File**: `BuildScripts/BuildWithVersion.ps1`
- **Usage**: `.\BuildScripts\BuildWithVersion.ps1 [options]`

#### Bash <PERSON>ript (Linux/CI)
- **File**: `BuildScripts/BuildWithVersion.sh`
- **Usage**: `./BuildScripts/BuildWithVersion.sh [options]`

#### Version Helper Script
- **File**: `BuildScripts/GetVersion.ps1`
- **Usage**: `.\BuildScripts\GetVersion.ps1 [options]`

## Usage Examples

### Building the Entire Solution
```powershell
# Windows
.\BuildScripts\BuildWithVersion.ps1

# Linux/CI
./BuildScripts/BuildWithVersion.sh
```

### Building Specific Projects
```powershell
# Build Appointments API
.\BuildScripts\BuildWithVersion.ps1 -Project "Services/AppointmentsApi"

# Build Web App
.\BuildScripts\BuildWithVersion.ps1 -Project "Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp"
```

### Build Options
```powershell
# Debug build
.\BuildScripts\BuildWithVersion.ps1 -Configuration Debug

# Clean build
.\BuildScripts\BuildWithVersion.ps1 -Clean

# Build without package restore
.\BuildScripts\BuildWithVersion.ps1 -Restore:$false
```

### Getting Version Information
```powershell
# Show detailed version info
.\BuildScripts\GetVersion.ps1

# Get just the version number
.\BuildScripts\GetVersion.ps1 -Short

# Get version as JSON
.\BuildScripts\GetVersion.ps1 -Json
```

## Integration with Existing Builds

### Visual Studio
- The versioning system works automatically when building through Visual Studio
- No additional configuration required
- Version information is displayed in the build output

### Command Line
```bash
# Standard dotnet build commands work with automatic versioning
dotnet build
dotnet build --configuration Release
dotnet publish --configuration Release
```

### CI/CD Pipelines
The versioning system works seamlessly with existing CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Build with versioning
  run: ./BuildScripts/BuildWithVersion.sh -c Release
```

## Version Information Access

### Runtime Access
Each built project includes a `version.json` file in its output directory:

```json
{
  "version": "2024.12.19.1430",
  "buildDate": "2024-12-19 14:30:15",
  "buildYear": "2024",
  "buildMonth": "12",
  "buildDay": "19",
  "buildTime": "1430"
}
```

### Assembly Information
Version information is embedded in assembly metadata:
- **AssemblyVersion**: `2024.12.19.0` (time component set to 0 for compatibility)
- **FileVersion**: `2024.12.19.1430` (full version with time)
- **InformationalVersion**: `2024.12.19.1430` (full version with time)

### Web Applications
For web applications, you can access version information at runtime:

```csharp
// Read version from file
var versionJson = File.ReadAllText(Path.Combine(AppContext.BaseDirectory, "version.json"));
var versionInfo = JsonSerializer.Deserialize<VersionInfo>(versionJson);

// Or from assembly
var assembly = Assembly.GetExecutingAssembly();
var version = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion;
```

## Benefits

1. **Automatic**: No manual version management required
2. **Unique**: Every build gets a unique version number
3. **Chronological**: Versions are naturally ordered by build time
4. **Traceable**: Easy to identify when a build was created
5. **Simple**: Uses standard date/time format that's human-readable
6. **Compatible**: Works with existing .NET tooling and CI/CD systems

## Troubleshooting

### Common Issues

1. **Permission Errors on Linux**:
   ```bash
   chmod +x BuildScripts/BuildWithVersion.sh
   ```

2. **PowerShell Execution Policy**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **Version File Not Created**:
   - Ensure the output directory exists
   - Check build output for any MSBuild errors

### Verification

To verify the versioning system is working:

1. Build any project
2. Check the build output for version messages
3. Look for `version.json` in the output directory
4. Use `GetVersion.ps1` to see current version

## Customization

### Changing Version Format
Edit `Directory.Build.props` to modify the version format:

```xml
<!-- Example: Add build number -->
<TeyaBuildVersion>$(BuildYear).$(BuildMonth).$(BuildDay).$(BuildTime).$(BuildNumber)</TeyaBuildVersion>
```

### Adding Custom Metadata
Add additional properties in `Directory.Build.props`:

```xml
<PropertyGroup>
  <BuildMachine>$([System.Environment]::MachineName)</BuildMachine>
  <BuildUser>$([System.Environment]::UserName)</BuildUser>
</PropertyGroup>
```

## Support

For questions or issues with the versioning system, please refer to this documentation or contact the development team.

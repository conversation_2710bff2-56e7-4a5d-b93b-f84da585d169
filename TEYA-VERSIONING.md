# Teya Health Enterprise Build Versioning

## Overview

This document describes the enterprise build versioning system implemented for Teya Health, using professional patterns that follow industry standards used by major technology companies.

## TeyaHealth Version Format

**Format**: `Major.Minor.Build.Revision`

**Example**: `1.0.50605.2`

### Industry Examples (Same Pattern)

- **Windows 10**: `10.0.19041.1234`
- **Visual Studio 2022**: `17.4.33213.308`
- **Office 365**: `16.0.14326.20404`
- **Windows 11**: `10.0.22000.1219`

### Our Implementation

- **Teya Health v1.0**: `1.0.50605.2`
  - **1** = Major version
  - **0** = Minor version  
  - **50605** = Build number (5=2025, 06=June, 05=Day 5)
  - **2** = Revision (2nd build today)

## Version Components Explained

### 1. Major Version (1)
- Product major version
- Manually managed for major releases
- Examples: TeyaHealth 1.x, 2.x

### 2. Minor Version (0)
- Product minor version
- Manually managed for feature releases
- Examples: TeyaHealth 1.0, 1.1

### 3. Build Number (50605)
- **Industry Pattern**: YMMDD format
- **Y** = Last digit of year (5 for 2025)
- **MM** = Month with leading zero (06 for June)
- **DD** = Day with leading zero (05 for 5th)
- **Automatically generated** based on build date

### 4. Revision (2)
- Daily build counter
- Resets to 1 each day
- Increments with each build on the same day
- Tracks development activity

## Why This Pattern Works

### 1. **Professional Standard**
- Used by major technology companies
- Recognized by enterprise customers
- Compatible with deployment tools

### 2. **Chronological Ordering**
- Build numbers naturally sort by date
- Easy to identify when a build was created
- No confusion about build sequence

### 3. **Enterprise Scale**
- Supports thousands of builds per day
- Works across multiple teams and products
- Integrates with enterprise deployment tools

### 4. **Automation Friendly**
- No manual intervention required
- Works seamlessly with CI/CD pipelines
- Consistent across all environments

## Usage Examples

### Standard Build
```powershell
# Build with default version 1.0.x.x
.\BuildScripts\TeyaBuild.ps1
```

### Product Version 2.1
```powershell
# Build version 2.1.x.x
.\BuildScripts\TeyaBuild.ps1 -MajorVersion 2 -MinorVersion 1
```

### Specific Project
```powershell
# Build specific project with TeyaHealth versioning
.\BuildScripts\TeyaBuild.ps1 -Project "Services\AppointmentsApi\Appointments.csproj"
```

### CI/CD Integration
```yaml
# GitHub Actions example
- name: TeyaHealth Enterprise Build
  run: |
    .\BuildScripts\TeyaBuild.ps1 -MajorVersion 1 -MinorVersion 0 -Configuration Release
```

## Build Artifacts

### version.json (TeyaHealth Format)
```json
{
  "product": {
    "name": "Teya Health Platform",
    "company": "Teya Health Corporation",
    "version": "1.0",
    "fullVersion": "1.0.50605.2"
  },
  "teyaHealth": {
    "buildNumber": "50605",
    "revision": 2,
    "year": "2025",
    "month": "06",
    "day": "05",
    "pattern": "Enterprise Industry Standard"
  },
  "build": {
    "configuration": "Release",
    "timestamp": "2025-06-05 18:12:55",
    "machine": "BUILD-SERVER"
  },
  "version": {
    "major": 1,
    "minor": 0,
    "build": 50605,
    "revision": 2,
    "full": "1.0.50605.2"
  }
}
```

## Enterprise Benefits

### 1. **Industry Standard Compliance**
- Follows patterns used by major companies
- Compatible with enterprise deployment tools
- Meets corporate governance requirements

### 2. **Professional Appearance**
- Looks like major enterprise software
- Builds confidence with enterprise customers
- Follows Fortune 500 company standards

### 3. **Traceability**
- Every build is uniquely identifiable
- Easy to correlate builds with dates
- Build frequency tracking
- Machine identification

### 4. **Release Management**
- Clear separation between product versions and builds
- Supports hotfix and patch releases
- Compatible with enterprise release processes

## File Structure

```
TeyaSourceCode/
├── .build/                          # TeyaHealth build system files
│   ├── build-counter.txt            # Daily revision counter
│   └── today.txt                    # Current day marker (50605 format)
├── BuildScripts/
│   └── TeyaBuild.ps1                # TeyaHealth enterprise build script
├── Directory.Build.props            # MSBuild TeyaHealth configuration
└── TEYA-VERSIONING.md               # This documentation
```

## Integration with Development

### Daily Development
```bash
# Standard dotnet commands work with TeyaHealth versioning
dotnet build                         # Gets version 1.0.50605.X
dotnet publish                       # Uses TeyaHealth version numbers
```

### Release Management
```powershell
# Major release (TeyaHealth 2.0)
.\BuildScripts\TeyaBuild.ps1 -MajorVersion 2 -MinorVersion 0

# Feature release (TeyaHealth 1.1)
.\BuildScripts\TeyaBuild.ps1 -MajorVersion 1 -MinorVersion 1
```

### Hotfix Process
```powershell
# Hotfix for version 1.0 (same major.minor, new build number)
.\BuildScripts\TeyaBuild.ps1 -MajorVersion 1 -MinorVersion 0
# Results in: 1.0.50605.X (where X is the daily revision)
```

## Best Practices

### For Development Teams
1. Use TeyaBuild script for all official builds
2. Let the system manage build numbers automatically
3. Only change Major/Minor for actual product releases
4. Include full version in bug reports and logs

### For Release Management
1. Plan Major/Minor versions strategically
2. Use build numbers to track development progress
3. Maintain compatibility with enterprise deployment tools
4. Document version changes in release notes

### For Enterprise Customers
1. Version numbers follow industry standards
2. Easy integration with enterprise environments
3. Compatible with existing enterprise tooling
4. Professional appearance and reliability

## Summary

This TeyaHealth enterprise versioning system ensures professional standards that match major technology companies. This provides:

- **Professional credibility** with enterprise customers
- **Industry-standard compliance** for corporate environments
- **Automatic build management** with zero manual intervention
- **Enterprise-grade traceability** and release management

The system is production-ready and follows the same patterns used by the world's largest software companies.
